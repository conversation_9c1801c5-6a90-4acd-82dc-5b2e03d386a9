import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges
} from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { DependentInformations } from '../../models';
import { MatButtonModule } from '@angular/material/button';
import { SignedDocuments, SignedDocumentsInfo } from 'src/app/pages/user-document/models';
import { MatSidenavModule } from '@angular/material/sidenav';
import { PdfViewerComponent } from '../../../../../user-document/pages/pdf-viewer/pdf-viewer.component';
import { LocalDatePipe } from 'src/app/shared/pipe';

const DEPENDENCIES = {
  MODULES: [CommonModule, MatButtonModule, SharedModule, MatSidenavModule],
  COMPONENTS: [PdfViewerComponent],
  PIPES: [LocalDatePipe]
};

@Component({
  selector: 'app-view-student-documents',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS, ...DEPENDENCIES.PIPES],
  templateUrl: './view-student-documents.component.html',
  styleUrl: './view-student-documents.component.scss'
})
export class ViewStudentDocumentsComponent extends BaseComponent implements OnChanges {
  @Input() selectedStudentDetails!: DependentInformations | undefined;
  @Input() studentDocuments!: Array<SignedDocuments>;

  documentInfo!: SignedDocumentsInfo;
  isPDFViewerSideNavOpen = false;

  @Output() closeSideNav = new EventEmitter<void>();

  constructor() {
    super();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['studentDocuments']?.currentValue) {
      this.studentDocuments = changes['studentDocuments']?.currentValue;
    }
  }

  openPDFViewer(documentInfo: SignedDocumentsInfo): void {
    this.isPDFViewerSideNavOpen = true;
    this.documentInfo = { ...documentInfo, isAgreementDone: true, accountManagerId: this.selectedStudentDetails?.accountManagerId ?? 0 };
  }

  closeViewAllDocument(): void {
    this.closeSideNav.emit();
  }
}
