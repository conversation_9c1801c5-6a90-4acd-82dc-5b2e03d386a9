import { CommonModule, DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { ClassTypes, LessonTypes } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { All } from 'src/app/pages/settings/pages/plan/models/plan-summary.model';
import { Instrument } from 'src/app/request-information/models';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CBGetResponse, CBResponse, IdNameModel } from 'src/app/shared/models';
import { CommonUtils } from 'src/app/shared/utils';
import moment from 'moment';
import { SharedModule } from 'src/app/shared/shared.module';
import { AuthService } from 'src/app/auth/services';
import { MatSidenavModule } from '@angular/material/sidenav';
import { GroupClassScheduleSummary, GroupClassFilters, DateFilterTypeEnum } from '../group-class/models';
import { GroupClassesService, GroupClassesEnrollService } from '../group-class/services';
import { ScheduleClassEnum, SelectedLessonInfo } from '../introductory-lesson/models';
import { BookIntroductoryLessonComponent } from '../introductory-lesson/pages/book-introductory-lesson/book-introductory-lesson.component';
import { SummerCampDetails } from 'src/app/pages/settings/pages/summer-camp-creation/models';
import { SummerCampEnrollService, SummerCampScheduleService } from 'src/app/pages/settings/pages/summer-camp-creation/services';
import { EnrollStudent, GroupAndSummerClassDetail } from '../summer-camp/models/summer-camp.model';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { EnsembleClassesService } from '../ensemble-class/services';
import { AssignedInstructors, AssignedInstruments, EnsembleClassForStudent } from '../ensemble-class/models';
import { MatTooltipModule } from '@angular/material/tooltip';
import { PaymentService } from '../../services';
import { MultiSelectComponent } from 'src/app/shared/components/multi-select/multi-select.component';
import { DateUtils } from 'src/app/shared/utils/date.utils';
import { LocalDatePipe } from 'src/app/shared/pipe';
import { SignUpForOptions } from 'src/app/auth/models';

const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    MatButtonModule,
    MatFormFieldModule,
    MatSelectModule,
    FormsModule,
    SharedModule,
    MatSidenavModule,
    MatTooltipModule
  ],
  COMPONENT: [BookIntroductoryLessonComponent, MultiSelectComponent],
  PIPES: [LocalDatePipe]
};

@Component({
  selector: 'app-summer-group-wrapper',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENT, ...DEPENDENCIES.PIPES],
  templateUrl: './summer-group-wrapper.component.html',
  styleUrl: './summer-group-wrapper.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SummerGroupWrapperComponent extends BaseComponent implements OnInit {
  @Input() scheduleClassType!: ScheduleClassEnum;
  @ViewChild(BookIntroductoryLessonComponent) bookIntroductoryLessonComponent!: BookIntroductoryLessonComponent;

  scheduleClassDetails: Array<GroupAndSummerClassDetail> = [];
  instruments!: Array<Instrument>;
  locations!: Array<SchoolLocations>;
  totalCount!: number;
  selectedScheduleClassDetails!: GroupAndSummerClassDetail | null;
  selectedDependentId!: number | undefined;
  showDependentInfo!: boolean;
  isBookYourLessonSideNavOpen!: boolean;
  bookedLessonInfo!: SelectedLessonInfo;

  lessonType = LessonTypes;
  classTypes = ClassTypes;
  scheduleClassEnum = ScheduleClassEnum;
  all = All;
  SignUpForOptions = SignUpForOptions;
  filters: GroupClassFilters = {
    instrumentIdFilter: {
      id: 1,
      defaultPlaceholder: 'All Instruments',
      placeholder: 'All Instruments',
      value: new Set(),
      totalCount: 0,
      isOpen: false,
      showSearchBar: true,
      showClassBorder: false,
      options: [] as Array<IdNameModel>
    },
    locationIdFilter: {
      id: 2,
      defaultPlaceholder: 'All Locations',
      placeholder: 'All Locations',
      value: new Set(),
      totalCount: 0,
      isOpen: false,
      showSearchBar: true,
      showClassBorder: false,
      options: [] as Array<IdNameModel>
    },
    ageGroupFilter: 0,
    currentDateFilter: this.datePipe.transform(new Date(), this.constants.dateFormats.yyyy_MM_dd),
    DateFilterType: DateFilterTypeEnum.UPCOMING_CLASSES
  };

  constructor(
    private readonly commonService: CommonService,
    private readonly cdr: ChangeDetectorRef,
    private readonly groupClassService: GroupClassesService,
    private readonly authService: AuthService,
    private readonly toasterService: AppToasterService,
    private readonly groupClassesEnrollService: GroupClassesEnrollService,
    private readonly summerCampScheduleService: SummerCampScheduleService,
    private readonly summerCampEnrollService: SummerCampEnrollService,
    private readonly ensembleClassScheduleService: EnsembleClassesService,
    protected readonly schedulerService: SchedulerService,
    protected readonly datePipe: DatePipe,
    private readonly paymentService: PaymentService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getInstruments();
    this.getLocations();
    this.getCurrentUserDetails();
    this.getScheduleDetailBasedOnClass();
  }

  getScheduleDetailBasedOnClass(): void {
    switch (this.scheduleClassType) {
      case ScheduleClassEnum.GROUP_CLASS:
        this.getGroupClassDetail();
        break;
      case ScheduleClassEnum.SUMMER_CAMP:
        this.getSummerCampDetail();
        break;
      case ScheduleClassEnum.ENSEMBLE_CLASS:
        this.getEnsembleClassDetail();
        break;
    }
  }

  getFilterParams() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      InstrumentIdFilter: [...this.filters.instrumentIdFilter.value],
      LocationIdFilter: [...this.filters.locationIdFilter.value],
      AgeGroupFilter: this.filters.ageGroupFilter,
      scheduleStartDate: DateUtils.getUtcRangeForLocalDate(new Date()).startUtc,
      scheduleEndDate: DateUtils.getUtcRangeForLocalDate(new Date()).endUtc,
      DateFilterType: this.filters.DateFilterType,
      Page: 1
    });
  }

  getGroupClassDetail(): void {
    this.showPageLoader = true;
    this.scheduleClassDetails = [];
    this.groupClassService
      .add(this.getFilterParams(), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<GroupClassScheduleSummary>) => {
          this.scheduleClassDetails = res.result.items
            .filter(({ groupClassScheduleSummary }) => this.checkIfEnrollDateIsPassed(groupClassScheduleSummary.enrollLastDate))
            .map(({ groupClassScheduleSummary }) => {
              return { ...groupClassScheduleSummary } as GroupAndSummerClassDetail;
            });
          this.totalCount = res.result.totalCount;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getSummerCampDetail(): void {
    this.showPageLoader = true;
    this.summerCampScheduleService
      .add(this.getFilterParams(), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SummerCampDetails>) => {
          this.scheduleClassDetails = res.result.items
            .filter(({ summerCampScheduleSummary }) => this.checkIfEnrollDateIsPassed(summerCampScheduleSummary.enrollLastDate))
            .map(({ summerCampScheduleSummary }) => {
              return { ...summerCampScheduleSummary } as GroupAndSummerClassDetail;
            });
          this.totalCount = res.result.totalCount;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getEnsembleClassDetail(): void {
    this.showPageLoader = true;
    this.ensembleClassScheduleService
      .add(this.getFilterParams(), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<EnsembleClassForStudent>) => {
          this.scheduleClassDetails = res.result.items.filter(({ enrollLastDate }) =>
            this.checkIfEnrollDateIsPassed(enrollLastDate)
          ) as GroupAndSummerClassDetail[];
          this.totalCount = res.result.totalCount;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getInstructorNames(array: AssignedInstructors[]): string {
    return array
      .slice(1)
      .map(item => item.instructorName)
      .join(', ');
  }

  getInstrumentNames(array: AssignedInstruments[]): string {
    return array
      .slice(1)
      .map(item => item.instrumentName)
      .join(', ');
  }

  checkIfEnrollDateIsPassed(enrollDate: string): boolean {
    return moment(enrollDate).isAfter(moment(), 'day');
  }

  getInstruments(): void {
    this.commonService
      .getInstruments()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          this.instruments = res.result.items;
          this.filters.instrumentIdFilter.options = res.result.items.map(instrument => ({
            id: instrument.instrumentDetail.id,
            name: instrument.instrumentDetail.name
          }));
          this.filters.instrumentIdFilter.totalCount = this.instruments.length;
          this.cdr.detectChanges();
        }
      });
  }

  getLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.filters.locationIdFilter.options = res.result.items.map(location => ({
            id: location.schoolLocations.id,
            name: location.schoolLocations.locationName
          }));
          this.filters.locationIdFilter.totalCount = this.locations.length;
          this.cdr.detectChanges();
        }
      });
  }

  getCurrentUserDetails(): void {
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          this.cdr.detectChanges();
        }
      });
  }

  showSelectedScheduleDetails(scheduleClassDetail: GroupAndSummerClassDetail): void {
    this.selectedScheduleClassDetails =
      this.selectedScheduleClassDetails && this.selectedScheduleClassDetails?.id === scheduleClassDetail?.id ? null : scheduleClassDetail;
  }

  setShowDependentInfo(showDependentInfo: boolean): void {
    if (showDependentInfo && !this.selectedScheduleClassDetails) {
      this.toasterService.error(this.constants.errorMessages.selectionPending.replace('{item}', 'a class'));
      return;
    }
    this.showDependentInfo = showDependentInfo;
  }

  onEnrollStudent(): void {
    this.paymentService.setUserPayment(null);
    if (!this.currentUser?.dependentDetails?.length) {
      this.enrollIndependentStudent();
      return;
    }

    if (!this.selectedDependentId) {
      this.toasterService.error(this.constants.errorMessages.selectionPending.replace('{item}', 'Student'));
      return;
    }

    this.bookedLessonInfo = this.getBookedLessonInfo();
    this.isBookYourLessonSideNavOpen = true;
  }

  getClassTypeFromScheduleType(): number {
    switch (this.scheduleClassType) {
      case ScheduleClassEnum.GROUP_CLASS:
        return ClassTypes.GROUP_CLASS;
      case ScheduleClassEnum.SUMMER_CAMP:
        return ClassTypes.SUMMER_CAMP;
      case ScheduleClassEnum.ENSEMBLE_CLASS:
        return ClassTypes.ENSEMBLE_CLASS;
    }
  }

  getBookedLessonInfo(): SelectedLessonInfo {
    const selectedLessonInfo = this.selectedScheduleClassDetails;
    return {
      ...selectedLessonInfo,
      childAge: selectedLessonInfo?.ageGroup,
      studentId: this.selectedDependentId,
      classType: this.getClassTypeFromScheduleType(),
      startDate: selectedLessonInfo?.scheduleStartDate ?? '',
      scheduleDate: selectedLessonInfo?.scheduleStartDate ?? '',
      endDate: selectedLessonInfo?.scheduleEndDate ?? '',
      scheduleStartTime: selectedLessonInfo?.scheduleStartTime,
      scheduleEndTime: selectedLessonInfo?.scheduleEndTime
    };
  }

  enrollIndependentStudent(): void {
    if (!this.selectedScheduleClassDetails) {
      this.toasterService.error(this.constants.errorMessages.selectionPending.replace('{item}', 'a class'));
      return;
    }
    this.selectedDependentId = this.currentUser?.dependentId;
    this.bookedLessonInfo = this.getBookedLessonInfo();
    this.isBookYourLessonSideNavOpen = true;
  }

  onConfirmGroupClassAppointment(): void {
    this.groupClassesEnrollService
      .add(this.getAddGroupClassOrSummerCampParams(), API_URL.groupClassEnrollmentDetails.enrollStudent)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.afterConfirmation(res);
          this.getGroupClassDetail();
          this.cdr.detectChanges();
        }
      });
  }

  onConfirmSummerCampAppointment(): void {
    this.summerCampEnrollService
      .add(this.getAddGroupClassOrSummerCampParams(), API_URL.summerCampEnrollmentDetails.enrollStudent)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.afterConfirmation(res);
          this.getSummerCampDetail();
          this.cdr.detectChanges();
        }
      });
  }

  onConfirmEnsembleClassAppointment(): void {
    this.ensembleClassScheduleService
      .add(this.getAddGroupClassOrSummerCampParams(), API_URL.summerCampEnrollmentDetails.enrollStudent)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.afterConfirmation(res);
          this.getEnsembleClassDetail();
          this.cdr.detectChanges();
        }
      });
  }

  onConfirmAppointment(): void {
    switch (this.scheduleClassType) {
      case ScheduleClassEnum.GROUP_CLASS:
        this.onConfirmGroupClassAppointment();
        break;
      case ScheduleClassEnum.SUMMER_CAMP:
        this.onConfirmSummerCampAppointment();
        break;
      case ScheduleClassEnum.ENSEMBLE_CLASS:
        this.onConfirmEnsembleClassAppointment();
        break;
    }
  }

  afterConfirmation(res: CBGetResponse<string>): void {
    // this.bookIntroductoryLessonComponent.onCloseModal();
    this.resetPageParams();
    if (res.result === 'Student has been successfully enrolled in the class.' || res.result === 'Student enrolled successfully.') {
      this.toasterService.success(res.result);
    } else {
      this.toasterService.error(res.result);
    }
  }

  resetPageParams(): void {
    this.showDependentInfo = false;
    this.selectedDependentId = undefined;
    this.selectedScheduleClassDetails = null;
  }

  getAddGroupClassOrSummerCampParams(): EnrollStudent {
    const params: any = {
      studentId: this.selectedDependentId,
      isPaid: true
    };

    switch (this.getClassTypeFromScheduleType()) {
      case ClassTypes.GROUP_CLASS:
        params.groupClassScheduleSummaryId = this.selectedScheduleClassDetails?.id;
        break;
      case ClassTypes.SUMMER_CAMP:
        params.summerCampScheduleSummaryId = this.selectedScheduleClassDetails?.id;
        break;
      case ClassTypes.ENSEMBLE_CLASS:
        params.ensembleClassesScheduleSummaryId = this.selectedScheduleClassDetails?.id;
        break;
    }

    return params;
  }
}
