import { Component, Inject, Input } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ConfirmationDialogConfig } from '../../models';
import { AppToasterService } from '../../services';
import { Constants } from '../../constants';

@Component({
  selector: 'app-confirmation-dialog',
  templateUrl: './confirmation-dialog.component.html',
  styleUrl: './confirmation-dialog.component.scss'
})
export class ConfirmationDialogComponent {
  reason!: string;
  price!: number | undefined;

  constructor(
    public dialogRef: MatDialogRef<ConfirmationDialogComponent>,
    private readonly toasterService: AppToasterService,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmationDialogConfig | null
  ) {
    this.price = this.data?.price;
  }

  onConfirm(): void {
    this.dialogRef.close({ isConfirmed: true, reason: this.reason, price: this.price });
  }

  onCancel(): void {
    this.dialogRef.close({ isConfirmed: false });
  }

  checkAmount(): void {
    if(!this.data?.price || !this.price) {
      return;
    }
    if(this.price > this.data?.price || this.price < 0) {
      this.price = this.data?.price;
      this.toasterService.error(Constants.errorMessages.priceValidation.replace('{price}', this.data?.price.toString()));
    }
  }
}
