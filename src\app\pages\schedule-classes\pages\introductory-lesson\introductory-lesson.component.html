<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isInstructorsSideNavOpen || isBookYourLessonSideNavOpen"
    mode="over"
    [disableClose]="true"
    position="end"
    fixedInViewport="true"
    [ngClass]="'sidebar-w-850'"
  >
    @if (isInstructorsSideNavOpen) {
    <app-instructor-list
      [selectedInstructorsIdFromParent]="selectedInstructorsId"
      [selectedLocationId]="schedulerInfoForm.getRawValue().locationId"
      [selectedInstrumentId]="schedulerInfoForm.getRawValue().instrumentId"
      (resetInstructorSideNav)="resetInstructorSideNav()"
      (closeInstructorSidNav)="toggleInstructorSideNav($event)"
      (setSelectedInstructorId)="setSelectedInstructorId($event)"
    ></app-instructor-list>
    } @if (isBookYourLessonSideNavOpen) {
    <app-book-introductory-lesson
      [scheduleInfo]="scheduleInfo"
      (closeEnrollmentSideNav)="toggleInstructorSideNav(false)"
      (confirmAppointments)="isPassReschedule ? onRescheduleAppointment() : onConfirmAppointments()"
      (scheduleIntroductoryLesson)="onConfirmAppointments($event)"
      (rePaymentForTheSchedule)="initPaymentProcess($event, rePaymentParams?.scheduleId, rePaymentParams?.studentId)"
      [isScheduleMakeUpLesson]="isScheduleMakeUpLesson"
    ></app-book-introductory-lesson>
    }
  </mat-sidenav>
  <mat-sidenav-content>
    <div class="header-tab-with-btn">
      <div class="title">
        {{ isScheduleMakeUpLesson ? 'Schedule Make-Up Lesson' : (setBasicInfoFilled ? 'Book' : 'Schedule An') + ' Introductory Lesson' }}
      </div>
      <div class="action-btn-wrapper">
        @if (setBasicInfoFilled) {
        <button mat-raised-button color="accent" class="mat-accent-btn back-btn action-btn" type="button" (click)="navigateBack()">
          Back
        </button>
        <button
          mat-raised-button
          color="primary"
          class="mat-primary-btn action-btn"
          type="button"
          (click)="toggleInstructorSideNav(false, true)"
        >
          Book Your Lesson
        </button>
        } @else {
        <button mat-raised-button color="primary" class="mat-primary-btn action-btn" type="button" (click)="getSchedulerInfoFormValue()">
          Continue
        </button>
        }
      </div>
    </div>

    <div class="auth-page-wrapper auth-page-with-header">
      <div class="o-card">
        <div class="o-card-body">
          <div class="schedule-introductory-lesson-content-wrapper">
            <div class="row">
              <div class="col-md-12 col-lg-4 content-detail-wrapper">
                @if (setBasicInfoFilled && scheduleInfo) {
                <div class="introductory-detail-wrapper">
                  <div class="schedule-information-title">
                    <div>Schedule Information</div>
                    <div class="primary-color pointer" *ngIf="!isScheduleMakeUpLesson" (click)="setBasicInfoFilled = false">Update</div>
                  </div>
                  <div class="schedule-basic-details">
                    <div class="schedule-info-header">
                      @if (isScheduleMakeUpLesson) {
                      <div class="group-name-age">{{ scheduleInfo.passName }} ({{ scheduleInfo.duration }})</div>
                      } @else {
                      <div class="group-name-age">
                        {{ scheduleInfo.lessonType === lessonType.IN_PERSON ? 'In-Person' : 'Duet™  Virtual' }}
                        {{ getInstrumentNameFromValue(scheduleInfo.instrumentId) }} Lesson
                        <div>({{ scheduleInfo.skillType }}, {{ schedulerService.getAgeLabelFromValue(scheduleInfo.childAge) }})</div>
                      </div>
                      }
                    </div>

                    <div class="location">
                      <img class="location-icon" [src]="constants.staticImages.icons.location" alt="" />
                      <div class="location-info-text text-gray">
                        {{ getLocationNameFromValue(scheduleInfo.locationId) }}
                      </div>
                    </div>
                    <div class="dependent-name">
                      <img class="dependent-icon" [src]="constants.staticImages.icons.profileCircle" alt="" />
                      <div class="dependent-name">
                        Schedule For
                        <span class="name">{{
                          getDependentNameFromValue(scheduleInfo.studentDetails && scheduleInfo.studentDetails[0].studentId) | titlecase
                        }}</span>
                      </div>
                    </div>
                  </div>
                  <ng-container
                    [ngTemplateOutlet]="slotOrStaffDetails && slotOrStaffDetails.showStaffDetails ? staffDetails : appointmentDetails"
                  ></ng-container>
                </div>
                } @else {
                <p>
                  Get ready for an exciting start to your musical journey a dynamic 30-minute session where you'll dive deep into new skills
                  with your instructor by your side. More than just a sample of what we offer it's the first step towards unlocking your
                  full potential. Buckle up and let's embark on this adventure together!
                </p>
                <p>
                  After your lesson is over, simply stop by the front desk and let us know that you'd like to enroll for weekly lessons. If
                  for whatever reason, you're not quite ready to take the plunge, there is absolutely no further obligation of any kind!
                </p>
                }
              </div>
              <div class="col-md-12 col-lg-8">
                <div class="schedule-introductory-info-form-wrapper">
                  <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : introductoryLessonTemplate"></ng-container>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>

<ng-template #introductoryLessonTemplate>
  <ng-container [ngTemplateOutlet]="setBasicInfoFilled ? bookLesson : basicInfoForm"></ng-container>
</ng-template>

<ng-template #basicInfoForm>
  <div class="introductory-lesson-wrapper">
    <form [formGroup]="schedulerInfoForm">
      <div class="field-wrapper mat-checkbox-custom">
        <mat-checkbox formControlName="isSpecialNeedsLesson">Is this lesson for a client with special needs?</mat-checkbox>
      </div>

      @if (currentUser?.dependentDetails && currentUser?.dependentDetails?.length) {
      <div class="field-wrapper">
        <label class="required mb-0">Schedule For</label>
        <div>
          <div class="btn-typed-options-wrapper">
            @if (currentUser?.userType === signUpForOptions.YOURSELF_AND_CHILD) {
            <div
              [ngClass]="{
                'btn-typed-option btn-typed-option-wrap': true,
                active: schedulerInfoForm.controls.studentId.value === currentUser?.dependentId
              }"
              (click)="setFormControlValue('studentId', currentUser?.dependentId ?? 0)"
            >
              {{ currentUser?.firstName | titlecase }} {{ currentUser?.lastName | titlecase }}
            </div>
            }
            @for (dependent of currentUser?.dependentDetails; track $index) {
            <div
              [ngClass]="{
                'btn-typed-option btn-typed-option-wrap': true,
                active: schedulerInfoForm.controls.studentId.value === dependent?.id
              }"
              (click)="setFormControlValue('studentId', dependent.id!)"
            >
              {{ dependent.firstName | titlecase }} {{ dependent.lastName | titlecase }}
            </div>
            }
          </div>
          <mat-error
            *ngIf="
              !schedulerInfoForm.controls.studentId?.value &&
              (schedulerInfoForm.controls.studentId?.touched || schedulerInfoForm.controls.studentId?.dirty)
            "
            class="mat-error-position"
          >
            <app-error-messages [control]="schedulerInfoForm.controls.studentId"></app-error-messages>
          </mat-error>
        </div>
      </div>
      }

      <div class="field-wrapper">
        <label class="required mb-0">Select Age</label>
        <div>
          <div class="btn-typed-options-wrapper">
            @for (age of constants.ageOptions; track $index) {
            <div
              [ngClass]="{
                'btn-typed-option': true,
                active: schedulerInfoForm.controls.childAge.value === age.value
              }"
              (click)="setFormControlValue('childAge', age.value); getInstruments(age.value)"
            >
              {{ age.label }}
            </div>
            }
          </div>
          <mat-error
            *ngIf="
              !schedulerInfoForm.controls.childAge.value &&
              (schedulerInfoForm.controls.childAge.touched || schedulerInfoForm.controls.childAge.dirty)
            "
            class="mat-error-position"
          >
            <app-error-messages [control]="schedulerInfoForm.controls.childAge"></app-error-messages>
          </mat-error>
        </div>
      </div>

      @if ( schedulerInfoForm.controls.childAge.value === constants.childAgeValues.sixToEight) {
      <div class="field-wrapper">
        <label class="required mb-0">Select Skill</label>
        <div>
          <div class="btn-typed-options-wrapper">
            <div
              [ngClass]="{
                'btn-typed-option': true,
                active: schedulerInfoForm.controls.skillType.value === constants.skillOptions.BEGINNER
              }"
              (click)="setFormControlValue('skillType', constants.skillOptions.BEGINNER)"
            >
              Beginner
            </div>
          </div>
          <mat-error
            *ngIf="
              !schedulerInfoForm.controls.skillType.value &&
              (schedulerInfoForm.controls.skillType.touched || schedulerInfoForm.controls.skillType.dirty)
            "
            class="mat-error-position"
          >
            <app-error-messages [control]="schedulerInfoForm.controls.skillType"></app-error-messages>
          </mat-error>
        </div>
      </div>
      }

      @if ( schedulerInfoForm.controls.childAge.value === constants.childAgeValues.nineToSeven || schedulerInfoForm.controls.childAge.value
      === constants.childAgeValues.eighteenPlus ) {
      <div class="field-wrapper">
        <label class="required mb-0">Select Skill</label>
        <div>
          <div class="btn-typed-options-wrapper">
            @for (skill of constants.skillOptions | keyvalue; track $index) {
            <div
              [ngClass]="{
                'btn-typed-option': true,
                active: schedulerInfoForm.controls.skillType.value === skill.value
              }"
              (click)="setFormControlValue('skillType', skill.value)"
            >
              {{ skill.value }}
            </div>
            }
          </div>
          <mat-error
            *ngIf="
              !schedulerInfoForm.controls.skillType.value &&
              (schedulerInfoForm.controls.skillType.touched || schedulerInfoForm.controls.skillType.dirty)
            "
            class="mat-error-position"
          >
            <app-error-messages [control]="schedulerInfoForm.controls.skillType"></app-error-messages>
          </mat-error>
        </div>
      </div>
      }

      <div class="field-wrapper">
        <label class="required mb-0">Select Lesson Type</label>
        <div>
          <div class="btn-typed-options-wrapper">
            @if ( !schedulerInfoForm.controls.childAge.value || schedulerInfoForm.controls.childAge.value ===
            constants.childAgeValues.twoAndHalfToFive || schedulerInfoForm.controls.childAge.value === constants.childAgeValues.sixToEight )
            {
            <div
              [ngClass]="{
                'btn-typed-option': true,
                active: schedulerInfoForm.controls.lessonType.value === lessonType.IN_PERSON
              }"
              (click)="setFormControlValue('lessonType', lessonType.IN_PERSON)"
            >
              {{ 'In-person Classes' }}
            </div>
            } @else { @for (lessonType of constants.lessonTypeValueOptions; track $index) {
            <div
              [ngClass]="{
                'btn-typed-option': true,
                active: schedulerInfoForm.controls.lessonType.value === lessonType.value
              }"
              (click)="setFormControlValue('lessonType', lessonType.value)"
            >
              {{ lessonType.label }}
            </div>
            } }
          </div>
          <mat-error
            *ngIf="
              !schedulerInfoForm.controls.lessonType.value &&
              (schedulerInfoForm.controls.lessonType.touched || schedulerInfoForm.controls.lessonType.dirty)
            "
            class="mat-error-position"
          >
            <app-error-messages [control]="schedulerInfoForm.controls.lessonType"></app-error-messages>
          </mat-error>
        </div>
      </div>

      <div class="field-wrapper" *ngIf="schedulerInfoForm.controls.childAge.value">
        <label class="required mb-0">Select Instrument</label>
        <div>
          <div class="btn-typed-options-wrapper">
            @for (instrumentType of instruments; track $index) {
            <div
              [ngClass]="{
                'btn-typed-option btn-typed-option-wrap': true,
                active: instrumentType.instrumentDetail.id === schedulerInfoForm.controls.instrumentId.value
              }"
              (click)="setFormControlValue('instrumentId', instrumentType.instrumentDetail.id)"
            >
              {{ instrumentType.instrumentDetail.name }}
            </div>
            }
          </div>
          <mat-error
            *ngIf="
              !schedulerInfoForm.controls.instrumentId.value &&
              (schedulerInfoForm.controls.instrumentId.touched || schedulerInfoForm.controls.instrumentId.dirty)
            "
            class="mat-error-position"
          >
            <app-error-messages [control]="schedulerInfoForm.controls.instrumentId"></app-error-messages>
          </mat-error>
        </div>
      </div>

      <div class="field-wrapper field-with-mat-inputs">
        <label class="required">Select location</label>
        <div class="w-100">
          <mat-form-field class="w-100">
            <mat-label>Which location do you plan to visit?</mat-label>
            <mat-select formControlName="locationId">
              <mat-option *ngFor="let location of locations" [value]="location.schoolLocations.id">
                {{ location.schoolLocations.locationName }}
              </mat-option>
            </mat-select>
            <mat-error>
              <app-error-messages [control]="schedulerInfoForm.controls.locationId"></app-error-messages>
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </form>
  </div>
</ng-template>

<ng-template #bookLesson>
  <app-available-introductory-lesson
    [scheduleInfo]="scheduleInfo"
    [selectedInstructorsIdFromParent]="selectedInstructorsId"
    [isScheduleMakeUpLesson]="isScheduleMakeUpLesson"
    [isPassReschedule]="isPassReschedule"
    (scheduleAppointmentsDetails)="setScheduleAppointmentsDetails($event)"
    (toggleInstructorSideNav)="toggleInstructorSideNav(true)"
  ></app-available-introductory-lesson>
</ng-template>

<ng-template #appointmentDetails>
  <ng-container [ngTemplateOutlet]="isScheduleMakeUpLesson ? makeUpLessonDetail : scheduleClassDetail"></ng-container>
</ng-template>

<ng-template #makeUpLessonDetail>
  <div class="appointment-detail">
    <p>You're almost ready to schedule your make-up lesson, but before you do, here are a few reminders!</p>
    <p>
      You receive a make-up pass one day after you have made your cancellation so long as that the cancelled lesson is in a month that has
      been paid for and has been cancelled at least 24 hours ahead of time. If you cancel a lesson for a month that has not yet been paid
      for, you will receive your make-up pass the day after that bill has been paid.
    </p>
    <p class="fw-bold">
      Unless you have already emailed us and received clearance to do so, if you schedule your lesson with an instructor other than your
      current teacher, the lesson will be canceled and the pass may be forfeited.
    </p>
  </div>
</ng-template>

<ng-template #scheduleClassDetail>
  <div class="appointment-detail">
    <div class="appointment-detail-content">
      <img [src]="constants.staticImages.icons.timeCircleClock" alt="" />
      <div class="appointment-info">30 Min</div>
    </div>
    <div class="appointment-detail-content">
      <img [src]="constants.staticImages.icons.circleDollar" alt="" />
      <div class="appointment-info">${{this.scheduleInfo.price??  constants.defaultAmountPayableForIntroductoryClasses}}</div>
    </div>
    <div class="appointment-detail-content">
      <img [src]="constants.staticImages.icons.profileCircle" alt="" />
      <div class="appointment-info">{{ schedulerService.getAgeLabelFromValue(scheduleInfo.childAge) }} Age</div>
    </div>
  </div>
  <p>You're only a few clicks (taps on mobile) away from scheduling your introductory lesson at Octopus Music School (lucky you)!</p>
  <p>
    After your lesson is over, stop by the front desk to sign up for lessons on a weekly basis. If you're not quite ready to take the
    plunge, there is no further obligation of any kind!
  </p>
</ng-template>

<ng-template #staffDetails>
  <div class="schedule-info-wrapper">
    <div class="text-content">Selected Instructor Bio</div>
    <div class="update-info-btn" (click)="showAppointmentDetails()">Close</div>
  </div>
  <div class="staff-img">
    <img
      [src]="
        slotOrStaffDetails.slotDetails?.instructorProfilePhoto
          ? slotOrStaffDetails.slotDetails?.instructorProfilePhoto
          : constants.staticImages.images.placeholderImage
      "
      alt=""
    />
  </div>
  <p class="staff-name">{{ slotOrStaffDetails.slotDetails?.instructorName | uppercase }}</p>
  <p class="course-desc-wrapper mb-0">
    {{ slotOrStaffDetails.slotDetails?.instructorBio }}
  </p>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
