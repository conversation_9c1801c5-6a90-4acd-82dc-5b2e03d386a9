import { PlanDetailWrapper } from "../../settings/pages/plan/models";

export interface SignedDocuments {
  signedDocuments: SignedDocumentsInfo;
}

export interface SignedDocumentsInfo {
  agreementDocumentId: number;
  accountManagerId: number;
  id: number | undefined;
  studentId: number | undefined;
  documentId: number | undefined;
  documentName: string;
  documentType: number | undefined;
  filePath: string;
  uploadDate: string;
  isAgreementDone: boolean;
  agreementDate: string;
  documentSendBy: string;
  fullFilePath: string;
  uploadLocalDate: string;
  recurringScheduleId: number;
  amount: number;
  isPaid: boolean;
  serviceFees: number;
  registrationFees: number;
  discountedAmount: number;
  planAmount: number;
  planId: number;
  daysOfSchedule: Array<number>;
  planDetails: PlanDetailWrapper[];
  planInstrument: string;
  planStartDate: string;
  planType: number;
  isRecurringDiscount: boolean;
  totalAmount: number;
  firstPayment: number;
  otherDependentPlans: Array<OtherDependentPlans>;
}

export interface OtherDependentPlans {
  dependenInformationId: number;
  dependentName: string;
  planId: number;
  planInstrument: string;
  planType: number;
  planAmount: number;
  totalAmount: number;
  discountedAmount: number;
  planStartDate: string;
  planEndDate: string;
  planDetails: PlanDetailWrapper[];
}
