import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MbscCalendarEvent, MbscEventClickEvent, MbscEventcalendarOptions, MbscModule, MbscPopup } from '@mobiscroll/angular';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { SchedulerService } from '../../services';
import { ClassTypes, ScheduleDetailsView } from '../../models';
import { CommonUtils } from 'src/app/shared/utils';
import { POPUP_OPTIONS } from 'src/app/shared/constants';
import { SchedulerDetailPopupComponent } from '../scheduler-detail-popup/scheduler-detail-popup.component';
import { MatButtonModule } from '@angular/material/button';
import moment from 'moment';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AssignedInstructors } from 'src/app/pages/schedule-classes/pages/ensemble-class/models';
import { Account } from 'src/app/auth/models/user.model';

const DEPENDENCIES = {
  MODULES: [MbscModule, CommonModule, MatIconModule, SharedModule, MatButtonModule, MatTooltipModule],
  COMPONENTS: [SchedulerDetailPopupComponent]
};

@Component({
  selector: 'app-schedule-week-view',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './schedule-week-view.component.html',
  styleUrl: './schedule-week-view.component.scss'
})
export class ScheduleWeekViewComponent extends BaseComponent {
  @Input() showScheduleForDate!: Date;
  @Input() schedulerData!: MbscCalendarEvent[];
  @Input() isLoading!: boolean;
  @Input() currentUser$!: Account | null;

  @ViewChild('showMoreEventsPopup', { static: false }) showMoreEventsPopup!: MbscPopup;
  @ViewChild('eventDetailsPopup', { static: false }) eventDetailsPopup!: MbscPopup;
  @ViewChild(SchedulerDetailPopupComponent) schedulerDetailPopupComponent!: SchedulerDetailPopupComponent;

  showMoreEventsAnchor!: EventTarget | null;
  detailsAnchor!: EventTarget | null;
  showMoreEventList!: MbscCalendarEvent[];
  selectedEvent!: ScheduleDetailsView | undefined;
  popupOptions = POPUP_OPTIONS;
  classTypes = ClassTypes;

  @Output() openScheduleUpdateModal = new EventEmitter<ScheduleDetailsView>();
  @Output() openAddSchedule = new EventEmitter<void>();
  @Output() refreshScheduleData = new EventEmitter<void>();

  calendarOptions: MbscEventcalendarOptions = {
    view: {
      schedule: {
        type: 'week',
        startTime: '08:00',
        endTime: '22:00',
        maxEventStack: 2,
        allDay: false,
        currentTimeIndicator: true,
        timeCellStep: 30
      }
    },

    onEventHoverIn: (args: MbscEventClickEvent) => {
      const moreEvents = args?.event?.['more'];
      if (moreEvents?.length) {
        this.openShowMoreEventsPopup(args, moreEvents);
      }
    }
  };

  constructor(public readonly schedulerService: SchedulerService) {
    super();
  }

  openShowMoreEventsPopup(args: MbscEventClickEvent, moreEvents: MbscCalendarEvent[]): void {
    this.showMoreEventList = moreEvents;
    this.showMoreEventsAnchor = args.domEvent.currentTarget || args.domEvent.target;
    this.showMoreEventsPopup?.open();
  }

  openEventDetailsPopup(args: ScheduleDetailsView | undefined, event: MouseEvent): void {
    if (args?.id) {
      this.selectedEvent = args;
    }
    this.detailsAnchor = event.currentTarget || event.target;
    this.eventDetailsPopup?.open();
  }

  closeEventDetailsPopup(shouldRefreshScheduleData: boolean): void {
    this.selectedEvent = undefined;
    if (shouldRefreshScheduleData) {
      this.refreshScheduleData.emit();
    }
    this.eventDetailsPopup.close();
    this.showMoreEventsPopup.close();
  }

  onOpenAddSchedule(): void {
    if (this.currentUser$?.userRoleId === this.constants.roleIds.ADMIN || this.currentUser$?.userRoleId === this.constants.roleIds.DESK_MANAGER) {
      this.openAddSchedule.emit();
    }
  }

  getInstructorNames(array: AssignedInstructors[]) {
    return array
      .slice(1)
      .map(item => item.instructorName)
      .join(', ');
  }

  getTimeDiff(start: string, end: string): number {
    return CommonUtils.calculateTimeDifference(new Date(start), new Date(end));
  }

  getDayInRange(startDate: string, scheduleDate: string): number {
    return moment(new Date(scheduleDate)).diff(new Date(startDate), 'days') + 1;
  }

  onEditLesson(scheduleDetail: ScheduleDetailsView): void {
    this.closeEventDetailsPopup(false);
    this.showMoreEventsPopup.close();
    this.openScheduleUpdateModal.emit(scheduleDetail);
  }
}
