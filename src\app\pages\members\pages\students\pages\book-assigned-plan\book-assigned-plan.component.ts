import { CommonModule, DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output } from '@angular/core';
import { FormArray, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatSidenavModule } from '@angular/material/sidenav';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { DependentInformations, StudentGrades } from '../../models';
import {
  AddSchedule,
  AddScheduleFormGroup,
  ClassTypes,
  InstructorAvaibility,
  SuggestedTimeSlot
} from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { MatInputModule } from '@angular/material/input';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CBGetResponse, CBResponse } from 'src/app/shared/models';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { Instructor } from 'src/app/schedule-introductory-lesson/models';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { PlanSummaryService } from 'src/app/pages/settings/pages/plan/services';
import { Instrument } from 'src/app/request-information/models';
import { PlanSummary } from 'src/app/pages/settings/pages/plan/models';
import { ContinueToCheckoutComponent } from '../continue-to-checkout/continue-to-checkout.component';
import { StudentGradeService } from '../../services';
import { Debounce } from 'src/app/shared/decorators';
import { CommonUtils } from 'src/app/shared/utils';
import { DateUtils } from 'src/app/shared/utils/date.utils';
import { MatRadioModule } from '@angular/material/radio';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    SharedModule,
    CommonModule,
    MatIconModule,
    MatFormFieldModule,
    MatSelectModule,
    FormsModule,
    MatSidenavModule,
    ReactiveFormsModule,
    MatInputModule,
    MatDatepickerModule,
    MatRadioModule
  ],
  COMPONENTS: [ContinueToCheckoutComponent]
};

@Component({
  selector: 'app-book-assigned-plan',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  providers: [provideNativeDateAdapter()],
  templateUrl: './book-assigned-plan.component.html',
  styleUrls: [
    './book-assigned-plan.component.scss',
    '../assign-plan-and-product/assign-plan-and-product.component.scss',
    '../../../../../scheduler/pages/scheduler-wrapper/pages/schedule/pages/add-schedule/add-schedule.component.scss'
  ]
})
export class BookAssignedPlanComponent extends BaseComponent implements OnChanges {
  @Input() selectedStudentDetails!: DependentInformations | undefined;
  @Input() selectedStudentPlan!: PlanSummary | undefined;

  bookPlanForm!: FormGroup<AddScheduleFormGroup>;
  bookPlanFormValue!: AddSchedule;
  locations!: Array<SchoolLocations>;
  instruments!: Array<Instrument>;
  instructors!: Array<Instructor> | undefined;
  maxDate = new Date();
  suggestedTimeSlots!: SuggestedTimeSlot[] | undefined;
  selectedTimeSlot!: string | null;
  selectedInstrumentName!: string;
  matchingGrade!: StudentGrades | undefined;
  studentGrades!: Array<StudentGrades>;
  isContinueToCheckoutSideNavOpen = false;

  @Output() closeSideNav = new EventEmitter<void>();
  @Output() closeAllSideNav = new EventEmitter<void>();

  constructor(
    private readonly commonService: CommonService,
    private readonly schedulerService: SchedulerService,
    private readonly cdr: ChangeDetectorRef,
    protected readonly planSummaryService: PlanSummaryService,
    private readonly toasterService: AppToasterService,
    private readonly studentGradeService: StudentGradeService
  ) {
    super();
  }

  ngOnChanges(): void {
    this.initBookPlanForm();
    this.getLocations();
    this.getInstruments();
    this.getStudentGrades(this.selectedStudentDetails?.id!);
    this.selectedInstrumentName =
      this.instruments.find(name => name.instrumentDetail.id === this.selectedStudentPlan?.instrumentId)?.instrumentDetail.name || '';
    this.setRequiredBasedOnCondition(
      'skillType',
      this.selectedInstrumentName !== 'General Music' && this.selectedInstrumentName !== 'Early Starters'
    );
  }

  initBookPlanForm(): void {
    this.bookPlanForm = new FormGroup<AddScheduleFormGroup>({
      id: new FormControl(undefined, { nonNullable: true }),
      classType: new FormControl(ClassTypes.RECURRING, { nonNullable: true, validators: [Validators.required] }),
      studentId: new FormControl(this.selectedStudentDetails?.id, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      lessonType: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      skillType: new FormControl('', { nonNullable: true }),
      instrumentId: new FormControl(this.selectedStudentPlan?.instrumentId!, { nonNullable: true }),
      locationId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      instructorId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      scheduleDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      daysOfSchedule: new FormArray([] as FormControl<number>[]),
      scheduleStartTime: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      scheduleEndTime: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      planId: new FormControl(this.selectedStudentPlan?.id, { nonNullable: true }),
      isSpecialNeedsLesson: new FormControl(false, { nonNullable: true }),
      isAllInstances: new FormControl(true, { nonNullable: true }),
      roomId: new FormControl(undefined, { nonNullable: true }),
      isClientRequest: new FormControl(true, { nonNullable: true, validators: [Validators.required] })
    });
  }

  getLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getInstruments(): void {
    this.commonService
      .getInstruments()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          this.instruments = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  get getInstructorAvailability(): InstructorAvaibility {
    return {
      classType: ClassTypes.RECURRING,
      instructorId: this.bookPlanForm.controls.instructorId.value,
      scheduleStartDate: DateUtils.getUtcRangeForLocalDate(this.bookPlanForm.controls.scheduleDate.value).startUtc,
      scheduleEndDate: DateUtils.getUtcRangeForLocalDate(this.bookPlanForm.controls.scheduleDate.value).endUtc,
      daysOfSchedule: this.bookPlanForm.controls.daysOfSchedule.value,
      isAllInstances: this.bookPlanForm.controls.isAllInstances.value,
      locationId: this.bookPlanForm.controls.locationId.value,
      planId: this.selectedStudentPlan?.id,
      duration: this.selectedStudentPlan?.plandetails.items[0].planDetail.duration!,
      instrumentId: this.selectedStudentPlan?.instrumentId!,
      skillType: this.bookPlanForm.controls.skillType.value,
      studentId: this.selectedStudentDetails?.id
    };
  }

  @Debounce(500)
  getInstructors(): void {
    if (
      this.getInstructorAvailability.scheduleStartDate &&
      this.getInstructorAvailability.locationId &&
      this.getInstructorAvailability.planId &&
      this.getInstructorAvailability.daysOfSchedule?.length
    ) {
      this.schedulerService
        .add(this.getInstructorAvailability, API_URL.scheduleLessonDetails.getAllInstructor)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: CBGetResponse<Instructor[]>) => {
            this.instructors = response.result;
            this.bookPlanForm.controls.instructorId.reset();
            this.cdr.detectChanges();
          }
        });
    }
  }

  getSuggestedTimeAndInstructors(): void {
    this.getInstructors();
    this.getSuggestedTime();
  }

  getSuggestedTime(): void {
    const availability = this.getInstructorAvailability;
    if (availability.classType && availability.instructorId && availability.scheduleStartDate) {
      this.schedulerService
        .add(this.getInstructorAvailability, API_URL.scheduleLessonDetails.getInstructorAvaibility)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: CBGetResponse<SuggestedTimeSlot[]>) => {
            this.suggestedTimeSlots = response.result.map(timeSlot => ({
              ...timeSlot,
              startTime: DateUtils.toLocal(timeSlot.startTime),
              endTime: DateUtils.toLocal(timeSlot.endTime)
            }));
            this.selectedTimeSlot = null;
            this.bookPlanForm.controls.scheduleStartTime.reset();
            this.cdr.detectChanges();
          }
        });
    }
  }

  getStudentGrades(studentId: number): void {
    this.studentGradeService
      .getList<CBResponse<StudentGrades>>(`${API_URL.studentGrades.getAllByStudentId}?studentId=${studentId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<StudentGrades>) => {
          this.studentGrades = res.result.items;
          this.getMatchingGrade();
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  setFormControlValue(controlName: string, value: number | string | boolean): void {
    (this.bookPlanForm.controls as any)[controlName].setValue(value);
  }

  getMatchingGrade(): void {
    this.matchingGrade = this.studentGrades?.find(
      studentGrade => studentGrade.studentGrade.instrumentId === this.selectedStudentPlan?.instrumentId
    );
    if (this.matchingGrade) {
      this.bookPlanForm.controls.skillType.setValue(this.getSkillBasedOnGrade(this.matchingGrade.studentGrade.grade));
    }
  }

  setLowestGradeBasedOnSkillType(skillType: string): number {
    const skillRange = this.constants.skillGradeRanges.find(range => range.skillType === skillType);
    return skillRange ? skillRange.minGrade : 0;
  }

  getSkillBasedOnGrade(grade: number): string {
    const skillRange = this.constants.skillGradeRanges.find(range => grade >= range.minGrade && grade <= range.maxGrade);
    return skillRange ? skillRange.skillType : '';
  }

  setStudentGrade(): void {
    this.studentGradeService
      .add(
        {
          instrumentId: this.selectedStudentPlan?.instrumentId,
          studentId: this.bookPlanForm.getRawValue().studentId,
          grade: this.setLowestGradeBasedOnSkillType(this.bookPlanForm.getRawValue().skillType)
        },
        API_URL.crud.createOrEdit
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  setRequiredBasedOnCondition(controlName: string, required: boolean): void {
    const control = this.bookPlanForm.get(controlName);
    if (required) {
      control?.setValidators([Validators.required]);
    } else {
      control?.clearValidators();
    }
    control?.updateValueAndValidity();
  }

  isDaySelected(dayValue: number): boolean | undefined {
    const daysOfSchedule = this.bookPlanForm.get('daysOfSchedule')?.value;
    return daysOfSchedule?.includes(dayValue);
  }

  getDayOfWeek(): void {
    const days = CommonUtils.getDaysOfWeekBetween(
      this.bookPlanForm.controls.scheduleDate.value,
      this.bookPlanForm.controls.scheduleDate.value
    );
    const daysOfSchedule = this.bookPlanForm.get('daysOfSchedule') as FormArray;
    daysOfSchedule.clear();
    days.forEach(day => daysOfSchedule.push(new FormControl(day, { nonNullable: true })));
  }

  setDaysOfWeek(dayValue: number): void {
    const daysOfSchedule = this.bookPlanForm.get('daysOfSchedule') as FormArray;
    const index = daysOfSchedule.value.indexOf(dayValue);
    if (index !== -1) {
      daysOfSchedule.removeAt(index);
    } else {
      if (daysOfSchedule.length < this.selectedStudentPlan?.plandetails.items[0].planDetail.visitsPerWeek!) {
        daysOfSchedule.push(new FormControl(dayValue, { nonNullable: true }));
      } else {
        this.toasterService.error(this.constants.errorMessages.maxDaysSelected);
        return;
      }
    }
    setTimeout(() => {
      this.getSuggestedTimeAndInstructors();
    }, 0);
  }

  setStartAndEndTime(selectedTimeSlot: SuggestedTimeSlot): void {
    this.selectedTimeSlot = `${selectedTimeSlot.startTime} - ${selectedTimeSlot.endTime}`;
    this.bookPlanForm.patchValue({
      scheduleStartTime: selectedTimeSlot.startTime,
      scheduleEndTime: selectedTimeSlot.endTime,
      roomId: selectedTimeSlot.roomId
    });
  }

  onSubmit(): void {
    if (this.bookPlanForm.invalid) {
      this.bookPlanForm.markAllAsTouched();
      return;
    }

    this.bookPlanForm.markAsUntouched();
    if (!this.matchingGrade) {
      this.setStudentGrade();
    }
    this.bookPlanFormValue = this.bookPlanForm.getRawValue();
    this.isContinueToCheckoutSideNavOpen = true;
  }

  closeAll(): void {
    this.isContinueToCheckoutSideNavOpen = false;
    this.closeAllSideNav.emit();
  }

  closeBookAssignedPlan(): void {
    this.closeSideNav.emit();
  }

  asIsOrder() {
    return 1;
  }
}
