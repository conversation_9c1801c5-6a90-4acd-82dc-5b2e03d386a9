import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../../shared.module';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import {
  AllCardsOfUser,
  CardDetailsResponse,
  PaymentParams
} from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FormsModule } from '@angular/forms';
import { AuthService } from 'src/app/auth/services';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from '../confirmation-dialog/confirmation-dialog.component';
import { takeUntil } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { Account, Address } from 'src/app/auth/models/user.model';
import { AppToasterService } from '../../services';
import { PaymentFormComponent } from '../payment-form/payment-form.component';
import { MatRadioModule } from '@angular/material/radio';
import { BaseComponent } from '../base-component/base.component';

const DEPENDENCIES = {
  MODULES: [MatButtonModule, SharedModule, FormsModule, CommonModule, MatCheckboxModule, MatIconModule, MatRadioModule],
  COMPONENTS: [PaymentFormComponent]
};

@Component({
  selector: 'app-card-method',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './card-method.component.html',
  styleUrl: './card-method.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CardMethodComponent extends BaseComponent implements OnInit {
  @Input() screen!: string;
  @Input() accManagerDetails!: Account | null;
  @Input() cardDetails!: AllCardsOfUser[];
  @Input() isPaymentFailed!: boolean;

  selectedCardDetail!: AllCardsOfUser;
  savedAddressDetails!: Address;
  paymentFormMode!: string | null;

  @Output() sendSelectedCardDetail = new EventEmitter<PaymentParams>();
  @Output() closeSideNav = new EventEmitter<void>();
  @Output() refreshCardDetails = new EventEmitter<void>();

  constructor(
    private readonly paymentService: PaymentService,
    private readonly authService: AuthService,
    private readonly cdr: ChangeDetectorRef,
    private readonly dialog: MatDialog,
    private readonly toasterService: AppToasterService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getAddress();
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.showPageLoader = true;
    if (changes['cardDetails']?.currentValue) {
      this.cardDetails = changes['cardDetails'].currentValue;
      this.cardDetails.length ? this.paymentFormMode = null : this.paymentFormMode = 'Add';
      this.selectedCardDetail = this.cardDetails.find((card: AllCardsOfUser) => card.isDefault) || this.cardDetails[0];
    }
    if (changes['accManagerDetails']?.currentValue) {
      this.accManagerDetails = changes['accManagerDetails'].currentValue;
    }
    this.showPageLoader = false;
    this.cdr.detectChanges();
  }

  getAddress(): void {
    const selectedCard = this.cardDetails.find((card: AllCardsOfUser) => card === this.selectedCardDetail);
    if (selectedCard && this.paymentFormMode === 'Edit') {
      this.savedAddressDetails = {
        firstName: selectedCard?.firstName ?? '',
        lastName: selectedCard?.lastName ?? '',
        address: selectedCard?.address ?? '',
        city: selectedCard?.city ?? '',
        state: selectedCard?.state ?? '',
        zipCode: selectedCard?.zip ?? ''
      };
    }
    else {
      this.savedAddressDetails = {
        firstName: this.accManagerDetails?.firstName ?? '',
        lastName: this.accManagerDetails?.lastName ?? '',
        address: this.accManagerDetails?.address ?? '',
        city: this.accManagerDetails?.city ?? '',
        state: this.accManagerDetails?.state ?? '',
        zipCode: this.accManagerDetails?.zipCode ?? ''
      };
    }
  }

  selectCard(card: AllCardsOfUser): void {
    this.selectedCardDetail = card;
    this.getAddress();
    this.sendSelectedCardDetail.emit(this.selectedCardDetail);
    this.cdr.detectChanges();
  }

  savedCardPayment(): void {
    if (!this.selectedCardDetail.customerVaultId) {
      this.toasterService.error(this.constants.errorMessages.noPaymentMethod);
      return;
    }
    this.showBtnLoader = true;
    this.paymentService.setUserPayment(this.getCardDetailsUsingSavedCard(this.selectedCardDetail));
    setTimeout(() => {
      this.showBtnLoader = false;
      this.cdr.detectChanges();
    }, 1000);
  }

  getCardDetailsUsingSavedCard(card: PaymentParams): CardDetailsResponse {
    return {
      isUsingSavedCard: true,
      customerVaultId: card.customerVaultId
    };
  }

  showAddCard(): void {
    this.paymentFormMode = 'Add';
    this.selectedCardDetail = {} as AllCardsOfUser;
    this.getAddress();
    this.cdr.detectChanges();
  }

  editCard(card: AllCardsOfUser): void {
    this.paymentFormMode = 'Edit';
    this.selectedCardDetail = card;
    this.getAddress();
    this.cdr.detectChanges();
  }

  deleteCard(card: AllCardsOfUser): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Card Information`,
        message: `Are you sure you want to delete this Card information?`
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result.isConfirmed) {
        this.deleteCardDetails(card);
        this.cdr.detectChanges();
      }
    });
  }

  deleteCardDetails(card: AllCardsOfUser): void {
    this.showPageLoader = true;
    this.paymentService
      .deleteCard(card.customerVaultId as string, this.accManagerDetails?.userId as number)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.constants.successMessages.deletedSuccessfully.replace('{item}', 'Card'));
          this.refreshCardDetails.emit();
          this.cdr.detectChanges();
        }
      });
  }

  closeSideNavFn(): void {
    this.closeSideNav.emit();
  }
}
