<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isBillingSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    ngClass="sidebar-w-850"
    [disableClose]="true">
    @if (isBillingSideNavOpen) {
        <div class="o-sidebar-wrapper">
            <div class="o-sidebar-header">
                <div class="title">Payment Methods</div>
                <div class="action-btn-wrapper">
                    <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button" (click)="isBillingSideNavOpen = false; getAllCustomerCards()">
                        Close
                    </button>
                </div>
            </div>
            <div class="divider"></div>
            <div class="o-sidebar-body">
                <app-payment-methods [accManagerDetails]="currentUser$" screen="billing-screen"></app-payment-methods>
            </div>
        </div>
    }
  </mat-sidenav>
</mat-sidenav-container>

<div class="plan-recurring-payment-container">
    <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : paymentContent"></ng-container>
</div>

<ng-template #paymentContent>
    <div class="o-card mb-4">
        <div class="o-card-body">
            <div class="card-header">
                <div class="card-title">
                    <mat-icon>description</mat-icon>
                    Plan Details
                </div>
            </div>

            <div class="plan-details-grid">
                <div class="plan-detail-item">
                    <div class="detail-label">Plan Name</div>
                    <div class="detail-value">Weekly Music Lessons ({{ planSummaryService.getPlanType(selectedPlanDetail!.planType) }} - {{ planSummaryService.getPlanSummary(selectedPlanDetail!.planDetails) }})</div>
                </div>

                <div class="plan-detail-item">
                    <div class="detail-label">Class Type</div>
                    <div class="detail-value">Recurring Plan</div>
                </div>

                <div class="plan-detail-item">
                    <div class="detail-label">Visit Per Week</div>
                    <div class="detail-value">Every {{ getDaysOfTheWeek }}</div>
                </div>

                <div class="plan-detail-item">
                    <div class="detail-label">Instrument</div>
                    <div class="detail-value">{{ selectedPlanDetail?.planInstrument }}</div>
                </div>

                <div class="plan-detail-item">
                    <div class="detail-label">Start From</div>
                    <div class="detail-value">{{ selectedPlanDetail?.planStartDate | localDate | date: 'mediumDate' }}</div>
                </div>

                <div class="plan-detail-item">
                    <div class="detail-label">Plan Amount</div>
                    <div class="detail-value plan-amount">{{ selectedPlanDetail?.planAmount | currency: 'USD' : true : '1.2-2' }}/Month</div>
                </div>
            </div>
            <div class="payment-note mt-3" *ngIf="selectedPlanDetail?.discountedAmount">
                <mat-icon>info</mat-icon>
                @if(selectedPlanDetail?.isRecurringDiscount) {
                    <span> <span class="bold">${{ selectedPlanDetail?.discountedAmount | number:'1.2-2' }}</span> discount will be applied to every future payment.</span>
                }
                @else {
                    <span> <span class="bold">${{ selectedPlanDetail?.discountedAmount | number:'1.2-2' }}</span> discount applied on first month's payment.</span>
                }
            </div>
        </div>
    </div>

    <div class="o-card mb-4" *ngIf="selectedPlanDetail?.otherDependentPlans?.length">
        <div class="o-card-body">
            <div class="card-header">
                <div class="card-title">
                    <mat-icon>description</mat-icon>
                    Existing Plan Details
                </div>
            </div>

            <ol class="existing-plans-wrapper mb-0">
                @for (otherPlan of selectedPlanDetail?.otherDependentPlans; track $index) {
                    <li class="existing-plans">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="detail-value">Weekly {{ otherPlan?.planInstrument }} Lessons ({{ planSummaryService.getPlanType(otherPlan!.planType) }} - {{ planSummaryService.getPlanSummary(otherPlan!.planDetails) }})</div>
                                <div class="detail-info">
                                    <div>For {{ otherPlan.dependentName }}</div>
                                    <div class="dot"></div>
                                    <div>Plan End Date: {{ otherPlan.planEndDate | localDate | date: 'mediumDate' }}</div>
                                </div>
                            </div>
                            <div>
                                <div class="plan-amount">{{ otherPlan.planAmount | currency: 'USD' : true : '1.2-2' }}/Month</div>
                            </div>
                        </div>
                        <div class="dotted-divider" *ngIf="!$last"></div>
                    </li>
                }
            </ol>
        </div>
    </div>

    <div class="o-card mb-4">
        <div class="o-card-body">
            <div class="card-header">
                <div class="card-title">
                    <mat-icon>payment</mat-icon>
                    Default Payment Method
                </div>
            </div>

            <div class="payment-method-wrapper">
                @if (defaultCard || defaultAch) {
                    <div class="payment-method-display">
                        @if (defaultCard) {
                        <div class="card-number">{{ defaultCard.ccType | uppercase}} **** {{ defaultCard.ccNum }}</div>
                        <div class="card-exp text-truncate">Expires {{ defaultCard.ccExpiry.slice(0, 2) }}/{{
                            defaultCard.ccExpiry.slice(2) }}</div>
                        }
                        @else if (defaultAch) {
                        <div class="account-content">
                            <div class="account-section">
                                <div class="section-header">
                                    <mat-icon class="section-icon">account_balance</mat-icon>
                                    <div class="section-title">Account Information</div>
                                </div>
                                <div class="section-body">
                                    <div class="detail-row">
                                        <span class="detail-label">Account Name:</span>
                                        <span class="detail-value">{{ defaultAch.accountName }}</span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Account Number:</span>
                                        <span class="detail-value"> {{ defaultAch.accountNumber }}</span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Routing Number:</span>
                                        <span class="detail-value"> {{ defaultAch.routingNumber }}</span>
                                    </div>
                                </div>
                            </div>
        
                            <div class="account-section">
                                <div class="section-header">
                                    <mat-icon class="section-icon">location_on</mat-icon>
                                    <div class="section-title">Billing Address</div>
                                </div>
                                <div class="section-body">
                                    <div class="detail-row">
                                        <span class="detail-value name-value">{{ defaultAch.firstName }} {{
                                            defaultAch.lastName }}</span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-value">{{ defaultAch.address }}</span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-value">{{ defaultAch.city }}, {{ defaultAch.state }} {{
                                            defaultAch.zip }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        }
                    </div>
                    <div class="payment-note mt-3">
                        <mat-icon>info</mat-icon>
                        <span>This is your default payment method for all future payments. You can change it from <span class="clickable" (click)="isBillingSideNavOpen = true"> billing section</span>.</span>
                    </div>
                }
                @else {
                    <div class="no-payment-method">
                        <mat-icon>warning</mat-icon>
                        <span>No default payment method set. Please add a payment method from <span class="clickable" (click)="isBillingSideNavOpen = true"> billing section</span> to continue.</span>
                    </div>
                }
            </div>
        </div>
    </div>

    <div class="o-card mb-4">
        <div class="o-card-body">
            <div class="card-header">
                <div class="card-title">
                    <mat-icon>receipt</mat-icon>
                    First Payment
                </div>
            </div>

            <div class="billing-breakdown">
                <div class="billing-item">
                    <div class="billing-label">Initial Month Plan Payment</div>
                    <div class="billing-value">{{ selectedPlanDetail?.firstPayment | currency: 'USD' : true : '1.2-2' }}</div>
                </div>

                <div class="billing-item">
                    <div class="billing-label">Service Charge</div>
                    <div class="billing-value">+{{ selectedPlanDetail?.serviceFees | currency: 'USD' : true : '1.2-2' }}</div>
                </div>

                <div class="billing-item">
                    <div class="billing-label">Registration Fees</div>
                    <div class="billing-value">+{{ selectedPlanDetail?.registrationFees | currency: 'USD' : true : '1.2-2' }}</div>
                </div>

                <div class="billing-item discount pb-0">
                    <div class="billing-label">Discount Applied</div>
                    <div class="billing-value">-${{ selectedPlanDetail?.discountedAmount | number:'1.2-2' }}</div>
                </div>

                <div class="dotted-divider"></div>

                <div class="billing-item total p-0">
                    <div class="billing-label">Total Amount</div>
                    <div class="billing-value">${{ calculateTotalAmount | number:'1.2-2' }}</div>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #showLoader>
    <div class="page-loader-wrapper">
        <app-content-loader></app-content-loader>
    </div>
</ng-template>