<h2 mat-dialog-title>{{ data?.title ?? "Confirm Action" }}</h2>
<mat-dialog-content>
  <p>{{ data?.message ?? "Are you sure you want to proceed?" }}</p>
  <mat-form-field *ngIf="data?.showReason">
    <mat-label>Reason</mat-label>
    <textarea matInput [(ngModel)]="reason" ></textarea>
  </mat-form-field>
  <mat-form-field *ngIf="data?.showPrice">
    <mat-label>Refund Amount</mat-label>
    <span matPrefix>$</span>
    <input type="number" matInput [(ngModel)]="price" (change)="checkAmount()" />
  </mat-form-field>
</mat-dialog-content>
<mat-dialog-actions [align]="'end'">
  <button mat-raised-button color="accent" class="mat-accent-btn back-btn" (click)="onCancel()" *ngIf="!data?.hideRejectBtn">
    {{ data?.rejectBtnName ?? "No" }}
  </button>
  <button mat-raised-button color="warn" class="mat-warn-btn" (click)="onConfirm()">
    {{ data?.acceptBtnName ?? "Yes" }}
  </button>
</mat-dialog-actions>
