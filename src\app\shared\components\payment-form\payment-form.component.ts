import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  SimpleChanges
} from '@angular/core';
import { AddressFormComponent } from '../address-form/address-form.component';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { SharedModule } from '../../shared.module';
import { BaseComponent } from '../base-component/base.component';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { CardDetailsResponse, PaymentParams } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { Account, Address } from 'src/app/auth/models/user.model';
import { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AppToasterService } from '../../services';
import { takeUntil } from 'rxjs';
import { AuthService } from 'src/app/auth/services';
import { API_URL } from '../../constants/api-url.constants';
import { TransactionTypes } from 'src/app/pages/shop/models';
declare const CollectJS: any;

const DEPENDENCIES = {
  MODULES: [CommonModule, ReactiveFormsModule, FormsModule, MatButtonModule, MatCheckboxModule, MatIconModule, SharedModule],
  COMPONENTS: [AddressFormComponent]
};

@Component({
  selector: 'app-payment-form',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './payment-form.component.html',
  styleUrls: ['./payment-form.component.scss', '../card-method/card-method.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PaymentFormComponent extends BaseComponent implements AfterViewInit, OnInit {
  @Input() screen: string = '';
  @Input() savedAddressDetails!: Address;
  @Input() cardDetails: PaymentParams[] = [];
  @Input() selectedCardDetail: PaymentParams = {} as PaymentParams;
  @Input() paymentFormMode!: string | null;
  @Input() accManagerDetails!: Account | null;
  @Input() isPaymentFailed!: boolean;

  isSaveCard = false;
  isvalidCardDetails = false;
  selectedCard: any;
  editCardForm: FormGroup;
  isFormValid = false;
  showLoaderAfterAdd = false;
  isAddressIncomplete = false;

  private fieldValidation = {
    ccnumber: false,
    ccexp: false,
    cvv: false
  };

  @Output() refreshCardDetails = new EventEmitter<void>();
  @Output() closePaymentForm = new EventEmitter<void>();

  constructor(
    protected readonly paymentService: PaymentService,
    private readonly formBuilder: FormBuilder,
    private readonly cdr: ChangeDetectorRef,
    private readonly toasterService: AppToasterService,
    private readonly authService: AuthService
  ) {
    super();
    this.editCardForm = this.formBuilder.group({
      cardExpiry: ['', [Validators.required, this.expiryDateValidator]]
    });
  }

  ngOnInit(): void {
    this.showPageLoader = true;
    this.setBtnLoader();
    if (this.paymentFormMode === 'Edit') {
      this.initEditCardForm();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['accManagerDetails']?.currentValue) {
      this.accManagerDetails = changes['accManagerDetails'].currentValue;
    }
    if (changes['isPaymentFailed']?.currentValue) {
      this.isPaymentFailed = changes['isPaymentFailed'].currentValue;
      this.setBtnLoader();
    }
  }

  setBtnLoader(): void {
    this.paymentService.showBtnLoader$.subscribe(res => {
      this.showBtnLoader = res;
      if (this.isPaymentFailed) {
        this.showBtnLoader = false;
      }
    });
  }

  getPaymentParams(response: any): PaymentParams {
    return {
      userId: this.accManagerDetails?.userId,
      token: response.token,
      ccNum: response.card.number,
      ccExpiry: response.card.exp,
      ccType: response.card.type,
      isDefault: false,
      transactionType: TransactionTypes.CARD,
      firstName: this.savedAddressDetails.firstName,
      lastName: this.savedAddressDetails.lastName,
      address: this.savedAddressDetails.address,
      city: this.savedAddressDetails.city,
      state: this.savedAddressDetails.state,
      zip: this.savedAddressDetails.zipCode,
      accountNumber: response.check.account,
      routingNumber: response.check.aba,
      accountName: response.check.name
    };
  }

  getAddress(user: Account): Address {
    return {
      firstName: user?.firstName,
      lastName: user?.lastName,
      address: user?.address,
      city: user?.city || '',
      state: user?.state || '',
      zipCode: user?.zipCode || ''
    };
  }

  ngAfterViewInit(): void {
    if (!(window as any)['CollectJS']) {
      return;
    }
    this.configureCollectJs();
    setTimeout(() => {
      this.showPageLoader = false;
      this.showLoaderAfterAdd = false;
      this.cdr.detectChanges();
    }, 1800);
  }

  configureCollectJs(): void {
    CollectJS.configure({
      validationCallback: this.handleValidationCallback.bind(this),
      callback: (response: any) => {
        response.ach = {};
        this.handleTokenizationCallback(response);
        this.cdr.detectChanges();
      },
      customCss: this.getCssStyles(),
      invalidCss: this.getCssStyles(),
      validCss: this.getCssStyles(),
      fields: this.getFieldConfigurations()
    });
  }

  private handleValidationCallback(field: string, status: boolean, message: string): void {
    const errorSelectors: { [key: string]: string } = {
      ccnumber: '#card-number-errors',
      ccexp: '#card-date-errors',
      cvv: '#card-cvv-errors',
      checkaba: '#ach-routing-errors',
      checkaccount: '#ach-number-errors',
      checkname: '#ach-name-errors',
      payButton: '#pay-button-errors'
    };

    if (field in this.fieldValidation) {
      this.fieldValidation[field as keyof typeof this.fieldValidation] = status;
    }

    this.isFormValid = Object.values(this.fieldValidation).every(valid => valid === true);

    const errorSelector = errorSelectors[field];
    if (errorSelector && status === false) {
      this.stopLoader();

      const errorDiv = document.querySelector(errorSelector) as HTMLDivElement;
      if (errorDiv) {
        errorDiv.innerHTML = message;
      }
    } else {
      const errorDiv = document.querySelector(errorSelector) as HTMLDivElement;
      if (errorDiv) {
        errorDiv.innerHTML = '';
      }
    }

    this.cdr.detectChanges();
  }

  private handleTokenizationCallback(response: {
    token?: string;
    error?: string;
    card?: { number: string; exp: string; type: string };
  }): void {
    this.isvalidCardDetails = true;
    if (response.token && !this.isAddressIncomplete) {
      this.showLoaderAfterAdd = true;
      this.showPageLoader = true;
      if (this.screen === 'billing-screen' || this.screen === 'continue-to-checkout') {
        this.paymentService
          .add(this.getPaymentParams(response), API_URL.payment.addNMICustomer)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: () => {
              this.showBtnLoader = false;
              this.toasterService.success(this.constants.successMessages.addedSuccessfully.replace('{item}', 'Customer Card'));
              this.refreshCardDetails.emit();
              this.cdr.detectChanges();
            },
            error: () => {
              this.showBtnLoader = false;
              this.cdr.detectChanges();
            }
          });
      } else {
        this.paymentService.setUserPayment(this.getCardDetails(response));
        this.paymentFormMode = 'Add';
        this.showPageLoader = false;
        this.showLoaderAfterAdd = false;
        this.closePaymentForm.emit();
      }
    } else {
      this.showBtnLoader = false;
    }
  }

  getCardDetails(response: any): CardDetailsResponse {
    return {
      token: response.token,
      number: response.card.number,
      expiry: response.card.exp,
      type: response.card.type,
      isSaveCard: this.isSaveCard,
      isUsingSavedCard: false,
      customerVaultId: response.customerVaultId,
      address: this.savedAddressDetails.address,
      city: this.savedAddressDetails.city,
      state: this.savedAddressDetails.state,
      zip: this.savedAddressDetails.zipCode,
      firstName: this.savedAddressDetails.firstName,
      lastName: this.savedAddressDetails.lastName,
      transactionType: TransactionTypes.CARD
    };
  }

  private getCssStyles(): { [key: string]: string } {
    return {
      color: '#000000',
      'background-color': '#F9F9F9',
      border: '1px solid #F9F9F9',
      'border-radius': '5px',
      padding: '14px',
      height: '55px',
      'margin-bottom': '10px'
    };
  }

  private getFieldConfigurations(): { [key: string]: any } {
    return {
      ccnumber: {
        selector: '#ccnumber',
        title: 'Card Number',
        placeholder: 'Enter Card Number'
      },
      ccexp: {
        selector: '#ccexp',
        title: 'Expiration Date',
        placeholder: 'MM / YY'
      },
      cvv: {
        selector: '#cvv',
        title: 'CVV',
        placeholder: 'CVV'
      }
    };
  }

  isAddressIncompleteFn(): void {
    this.paymentService.isAddressIncomplete$.pipe(takeUntil(this.destroy$)).subscribe(isIncomplete => {
      this.isAddressIncomplete = isIncomplete;
      this.cdr.markForCheck();
    });
  }

  onAddCard(event: Event): void {
    this.isAddressIncompleteFn();
    if (this.isAddressIncomplete) {
      this.toasterService.error(this.constants.errorMessages.addressIncomplete);
      return;
    }
    if (this.isFormValid) {
      CollectJS.startPaymentRequest(event);
      this.cdr.detectChanges();
    } else {
      this.showBtnLoader = false;
      this.toasterService.error(this.constants.errorMessages.invalidDetails.replace('{item}', 'Card'));
      this.cdr.detectChanges();
    }
  }

  stopLoader(): void {
    this.showBtnLoader = false;
    this.isFormValid = false;
    this.cdr.detectChanges();
  }

  onSaveAddress(addressData: Address): void {
    this.savedAddressDetails = addressData;
  }

  initEditCardForm(): void {
    if (this.selectedCardDetail) {
      const initialExpiry = this.selectedCardDetail.ccExpiry
        ? this.selectedCardDetail.ccExpiry.slice(0, 2) + '/' + this.selectedCardDetail.ccExpiry.slice(2)
        : '';

      this.editCardForm = this.formBuilder.group({
        cardExpiry: [initialExpiry, [Validators.required, this.expiryDateValidator]]
      });
    } else {
      this.editCardForm = this.formBuilder.group({
        cardExpiry: ['', [Validators.required, this.expiryDateValidator]]
      });
    }
  }

  expiryDateValidator(control: AbstractControl): ValidationErrors | null {
    const value = control.value;
    if (!value) {
      return { required: true };
    }

    const regex = /^(0[1-9]|1[0-2])\/([0-9]{2})$/;
    if (!regex.test(value)) {
      return { invalidFormat: true };
    }

    const [month, year] = value.split('/');
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear() % 100;
    const currentMonth = currentDate.getMonth() + 1;

    const expYear = parseInt(year, 10);
    const expMonth = parseInt(month, 10);

    if (expYear < currentYear || (expYear === currentYear && expMonth < currentMonth)) {
      return { expired: true };
    }

    return null;
  }

  formatExpiryDate(event: any): void {
    const input = event.target;
    let value = input.value.replace(/\D/g, '');

    if (value.length > 0) {
      if (value.length > 2) {
        value = value.substring(0, 2) + '/' + value.substring(2, 4);
      }

      const month = parseInt(value.substring(0, 2), 10);
      if (month > 12) {
        value = '12' + value.substring(2);
      }

      input.value = value;
    }

    this.validateCardExpiry(input.value);
  }

  validateCardExpiry(value: string): boolean {
    const errorDiv = document.querySelector('#card-date-errors') as HTMLDivElement;

    if (!value) {
      if (errorDiv) {
        errorDiv.innerHTML = 'Expiration date is required';
      }
      return false;
    }

    const regex = /^(0[1-9]|1[0-2])\/(\d{2})$/;
    if (!regex.test(value)) {
      if (errorDiv) {
        errorDiv.innerHTML = 'Invalid format (MM/YY)';
      }
      return false;
    }

    const [month, year] = value.split('/');
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear() % 100;
    const currentMonth = currentDate.getMonth() + 1;

    const expYear = parseInt(year, 10);
    const expMonth = parseInt(month, 10);

    if (expYear < currentYear || (expYear === currentYear && expMonth < currentMonth)) {
      if (errorDiv) {
        errorDiv.innerHTML = 'Card has expired';
      }
      return false;
    }

    if (errorDiv) {
      errorDiv.innerHTML = '';
    }

    return true;
  }

  updateCard(): void {
    if (this.editCardForm.invalid) {
      return;
    }
    this.showBtnLoader = true;
    this.paymentService.update(this.getEditPaymentParams(), API_URL.payment.updateNMICustomer).subscribe({
      next: () => {
        this.showBtnLoader = false;
        this.toasterService.success(this.constants.successMessages.updatedSuccessfully.replace('{item}', 'Customer Card'));
        this.refreshCardDetails.emit();
        this.cdr.detectChanges();
      },
      error: () => {
        this.showBtnLoader = false;
        this.cdr.detectChanges();
      }
    });
  }

  getEditPaymentParams(): PaymentParams {
    const expiryDate = this.editCardForm.get('cardExpiry')?.value;
    const expiryDateWithoutThirdChar = expiryDate.slice(0, 2) + expiryDate.slice(3);
    return {
      userId: this.accManagerDetails?.userId,
      ccExpiry: expiryDateWithoutThirdChar,
      customerVaultId: this.selectedCardDetail.customerVaultId,
      firstName: this.savedAddressDetails.firstName,
      lastName: this.savedAddressDetails.lastName,
      address: this.savedAddressDetails.address,
      city: this.savedAddressDetails.city,
      state: this.savedAddressDetails.state,
      zip: this.savedAddressDetails.zipCode
    };
  }
}
