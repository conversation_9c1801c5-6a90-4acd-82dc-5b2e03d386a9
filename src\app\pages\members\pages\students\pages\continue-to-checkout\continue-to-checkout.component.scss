@import 'src/assets/scss/theme/_mixins.scss';
@import 'src/assets/scss/variables';

.plan-details-wrapper {
  padding: 0px 60px;

  .sub-title {
    font-weight: 700;
    margin-bottom: 10px;
  }

  .title {
    margin-bottom: 15px;
    font-weight: 700;
    font-size: 22px;
  }

  .plan-details {
    .discount-toggle {
      cursor: pointer;

      .discount-link {
        color: $primary-color;
        font-weight: 700;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    .plan-details-content {
      .sub-title {
        font-size: 15px;
        color: $gray-text;
      }

      .product-sub-title {
        font-size: 16px;
        color: $gray-text;
      }

      .plan-billing-sub-title {
        font-size: 15px;
        color: $gray-text;
        margin-bottom: 15px;
      }

      .plan-content {
        @include flex-content-align-center;
        font-size: 17px;
        color: $original-black-color;
        font-weight: 700;
        margin-bottom: 15px;
      }

      .discount {
        color: $primary-color !important;
      }

      .space-between {
        @include flex-content-space-between;
      }
    }
  }

  ::ng-deep {
    .mat-checkbox-custom .mdc-label {
      color: $gray-text !important;
    }

    .mat-checkbox-custom.mat-mdc-checkbox-checked .mdc-label {
      color: $primary-color !important;
    }

    .mdc-form-field > label {
      padding-left: 0px !important;
      color: $original-black-color !important;
    }

    .mat-mdc-radio-checked {
      .mdc-label {
        color: $primary-color !important;
      }
    }
  }
}
