<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    @if (documentInfo) {
    <div class="title">
      @if ((documentInfo.isAgreementDone && documentInfo.isPaid) || isFromAdmin) { View Document } @else if (showPaymentTemplate &&
      !isRePayment) { Payment } @else if (isRePayment) { Retry Payment } @else { Accept T&C }
    </div>
    } @else {
    <div class="title">View Document</div>
    }
    <div class="action-btn-wrapper">
      <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button" (click)="closeSideNavFun()">Close</button>
      <ng-container>
        <button
          *ngIf="(!documentInfo?.isAgreementDone || !documentInfo?.isPaid)"
          mat-raised-button
          color="primary"
          class="mat-primary-btn"
          type="button"
          (click)="onPayClick()"
          [disabled]="!(showPaymentTemplate || isAgreementDone)"
          [appLoader]="showBtnLoader"
        >
        @switch (true) {
          @case (showPaymentTemplate && !isRePayment) {
            Proceed to Pay
          }
          @case (isRePayment) {
            Retry Payment
          }
          @case (!showPaymentTemplate && !isRePayment) {
            Accept T&C
          }
        }
        </button>
      </ng-container>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <ng-container [ngTemplateOutlet]="showPaymentTemplate ? showPaymentTemp : acceptTermsAndConditionTemp"></ng-container>
  </div>
</div>

<ng-template [ngIf]="showPageLoader">
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>

<ng-template #acceptTermsAndConditionTemp>
  <iframe
    [src]="documentInfo?.fullFilePath || selectedPlanDocument | safe"
    frameborder="1"
    width="100%" title="card"
    (load)="onDocumentLoaded()"></iframe>
  <mat-checkbox
    *ngIf="documentInfo && !documentInfo?.isAgreementDone && !showPageLoader"
    [(ngModel)]="isAgreementDone"
    (ngModelChange)="isAgreementSubmitted = true"
    class="mat-checkbox-custom mt-3"
  >
    Accept the Terms and Conditions
  </mat-checkbox>
</ng-template>

<ng-template #showPaymentTemp>
  <app-plan-recurring-payment [selectedPlanDetail]="documentInfo" [currentUser$]="currentUser" (defaultCardDetail)="defaultCardDetail = $event" (defaultAchDetail)="defaultAchDetail = $event"></app-plan-recurring-payment>
</ng-template>
