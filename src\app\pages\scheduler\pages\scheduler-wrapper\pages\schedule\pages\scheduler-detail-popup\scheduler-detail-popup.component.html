<ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : eventDetailsPopup"></ng-container>

<ng-template #eventDetailsPopup>
  <ng-container [ngTemplateOutlet]="showCancelLessonView ? cancelLessonPopup : popupContent"></ng-container>
</ng-template>

<ng-template #popupContent>
  <ng-container [ngTemplateOutlet]="selectedEvent?.isLeave ? leaveEventPopup : eventDetailPopup"></ng-container>
</ng-template>

<ng-template #leaveEventPopup>
  <div class="event-details">
    <div class="event-detail-header mt-0">
      <div class="d-flex">
        <div
          class="event-detail-border"
          [ngStyle]="{
            'background-color': 'darkgray'
          }"
        ></div>
        <div class="event-detail-header-content">
          <div class="event-instrument-name pointer" (click)="openInstructorDetails(selectedEvent?.instructorId)">
            {{ selectedEvent?.name | titlecase }} is unavailable
          </div>
        </div>
      </div>
    </div>
    <div class="divider"></div>
    <div class="event-details-body">
      <div class="event-basic-info">
        <div class="schedule-content">
          <img [src]="constants.staticImages.icons.timeCircleClock" alt="" />
          {{ selectedEvent?.start | localDate | date : 'shortTime' }}-{{ selectedEvent?.end | localDate | date : 'shortTime' }} -
          {{ selectedEvent?.leaveDate | date : constants.fullDate }}
        </div>
        <div class="schedule-content mt-2">
          <img [src]="constants.staticImages.icons.location" alt="" />
          <span>{{ selectedEvent?.locationName }}</span>
        </div>
        <div class="schedule-content mt-2">
          <img [src]="constants.staticImages.icons.checkCircle" alt="" />
          <span matTooltip="Reason">{{ selectedEvent?.reason }}</span>
        </div>
      </div>
    </div>
    <div class="dotted-divider mt-0"></div>
    <div class="action-btn-wrapper">
      <button mat-raised-button color="accent" class="mat-accent-btn me-2" type="button" (click)="onClosePopup(false)">Close</button>
    </div>
  </div>
</ng-template>

<ng-template #eventDetailPopup>
  <div class="event-details">
    <div class="event-detail-header">
      <div class="d-flex">
        <div
          class="event-detail-border"
          [ngStyle]="{
            'background-image': scheduleDetail?.isDraftSchedule
              ? 'url(' + constants.staticImages.icons.draftScheduleBorder + ')'
              : 'url(' + schedulerService.getClassTypeBorder(scheduleDetail?.classType) + ')',
            'background-color': scheduleDetail?.isDraftSchedule ? constants.colors.blackColor : scheduleDetail?.instrumentFontColor
          }"
        ></div>
        <div class="event-detail-header-content">
          <div
            class="event-instrument-name"
            [ngStyle]="{
              color: scheduleDetail?.isDraftSchedule
                ? constants.colors.blackColor
                : scheduleDetail?.instrumentFontColor ?? constants.colors.blueColor
            }"
          >
            <span *ngIf="scheduleDetail?.isCancelSchedule">
              @if (scheduleDetail?.isLateCancelSchedule) {
                Late Canceled:
              } @else {
                Canceled:
              }
            </span>
            @if (scheduleDetail?.classType === classTypes.SUMMER_CAMP) {
            <span class="camp-name"
              >{{ selectedEvent?.campName | titlecase }} ({{
                schedulerService.getNumberOfDays(selectedEvent?.campStartDate!, selectedEvent?.campEndDate!)
              }}d)</span
            >
            } @else { @switch (scheduleDetail?.classType) { @case (classTypes.GROUP_CLASS) {
            {{ scheduleDetail?.groupClassName | titlecase }}
            } @case (classTypes.MAKE_UP) {
            {{ scheduleDetail?.instrumentName }} Make-Up Lesson } @case (classTypes.INTRODUCTORY) { Introductory
            {{ scheduleDetail?.instrumentName }} Lesson } @case (classTypes.ENSEMBLE_CLASS) {
            {{ scheduleDetail?.ensembleClassName }}
            } @default {
            {{ scheduleDetail?.instrumentName }} Lesson } }
            <span *ngIf="scheduleDetail?.start"> ({{ getTimeDiff(scheduleDetail!.start, scheduleDetail!.end) }}) </span>
            }
          </div>
          <div class="class-lesson-type">
            @if (scheduleDetail?.isSpecialNeedsLesson) {
            <div class="badge special">Special Needs</div>
            <div class="dot"></div>
            } @if (scheduleDetail?.isDraftSchedule) {
            <div class="badge draft">Draft</div>
            <div class="dot"></div>
            }
            {{ schedulerService.getClassType(scheduleDetail?.classType) }}
            <div class="dot"></div>
            {{ schedulerService.getLessonType(scheduleDetail?.lessonType) }}
          </div>
        </div>
      </div>
   
      <div *ngIf="!isFromViewStudent">
        <img [src]="constants.staticImages.icons.expand" alt="" class="navigate-img" (click)="navigateToViewSchedule()" />
      </div>
    </div>
    <div class="divider"></div>
    <div class="event-details-body">
      <div class="event-basic-info">
        <div class="schedule-content">
          <img [src]="constants.staticImages.icons.timeCircleClock" alt="" />
          {{ scheduleDetail?.start | localDate | date : 'shortTime' }}-{{ scheduleDetail?.end | localDate | date : 'shortTime' }} -
          {{ scheduleDetail?.start | localDate | date : constants.fullDate }}
        </div>
        <div class="schedule-content mt-2">
          <img [src]="constants.staticImages.icons.location" alt="" />
          <span>{{ scheduleDetail?.locationName }}</span>
        </div>
        @if (scheduleDetail?.roomName) {
        <div class="schedule-content mt-2">
          <img [src]="constants.staticImages.icons.roomIcon" alt="" />
          {{ scheduleDetail?.roomName }}
        </div>
        } @if (scheduleDetail?.classType === classTypes.SUMMER_CAMP) {
        <div class="schedule-content mt-2">
          <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
          {{ scheduleDetail?.campStartDate | localDate | date : 'mediumDate' }} -
          {{ scheduleDetail?.campEndDate | localDate | date : 'mediumDate' }}
        </div>
        }
      </div>
      <div class="dotted-divider"></div>

      <div class="event-instructor-detail">
        <div class="title">Instructor Details</div>
        <div class="d-flex justify-content-between" *ngIf="scheduleDetail?.classType !== classTypes.ENSEMBLE_CLASS">
          <div>
            <div class="schedule-content">
              @if (scheduleDetail?.instructorProfilePhoto) {
              <img [src]="scheduleDetail?.instructorProfilePhoto" class="instructor-img" alt="" />
              } @else {
              <div class="placeholder-name">
                <div>
                  {{ getInitials(scheduleDetail?.instructorName) | uppercase }}
                </div>
              </div>
              }
              <div class="pointer" (click)="openInstructorDetails(scheduleDetail?.instructorId)">{{ scheduleDetail?.instructorName | titlecase }}</div>
            </div>
            <div class="schedule-content mt-2">
              <img [src]="constants.staticImages.icons.phone" class="me-2" alt="" />
              {{ scheduleDetail?.instructorPhoneNo }}
            </div>
          </div>
          <div>
            <div class="schedule-content h-20">
              @if ( (scheduleDetail?.instructorInstrument)!.length <= 1 && scheduleDetail?.classType !== classTypes.SUMMER_CAMP ) {
              <img [src]="constants.staticImages.icons.musicWave" class="me-1" alt="" />
              {{ scheduleDetail?.instrumentName }}
              <span class="primary-color ms-1 fw-bold" *ngIf="(scheduleDetail?.instructorInstrument)!.length > 1">{{
                scheduleDetail!.instructorInstrument[0].instrumentGrade
              }}</span>
              }
            </div>
            <div class="schedule-content mt-2 w-170" *ngIf="scheduleDetail">
              <img [src]="constants.staticImages.icons.email" alt="" />
              <span class="text-truncate" [matTooltip]="scheduleDetail.instructorEmail">{{ scheduleDetail.instructorEmail }}</span> 
            </div>
          </div>
        </div>

        @if (scheduleDetail?.classType == classTypes.ENSEMBLE_CLASS){ @for (assignedInstructor of scheduleDetail?.assignedInstructors; track
        $index) {
        <div class="d-flex justify-content-between mb-2">
          <div>
            <div class="schedule-content">
              @if (scheduleDetail?.instructorProfilePhoto) {
              <img [src]="scheduleDetail?.instructorProfilePhoto" class="instructor-img" alt="" />
              } @else {
              <div class="placeholder-name">
                <div>
                  {{ getInitials(assignedInstructor?.name) | uppercase }}
                </div>
              </div>
              }
              <div class="pointer" (click)="openInstructorDetails(scheduleDetail?.instructorId)">{{ assignedInstructor?.name | titlecase }}</div>
            </div>
            <div class="schedule-content mt-2">
              <img [src]="constants.staticImages.icons.phone" class="me-2" alt="" />
              {{ assignedInstructor?.phoneNumber }}
            </div>
          </div>

          <div>
            <div class="schedule-content mt-2 w-170">
              <img [src]="constants.staticImages.icons.email" alt="" />
              <span class="text-truncate" [matTooltip]="assignedInstructor && assignedInstructor.email">{{ assignedInstructor?.email }}</span> 
            </div>          
          </div>
        </div>
        <div class="schedule-content ">
          @if (assignedInstructor.instruments && assignedInstructor.instruments.length) {
          <img [src]="constants.staticImages.icons.musicWave" class="me-2" alt="" />
          @for (instruments of assignedInstructor.instruments; track $index) {
          <ng-container>
            {{ instruments.instrumentName }}
            <span class="primary-color ms-1 fw-bold">{{ instruments.instrumentGrade }}</span>
            <div class="dot" *ngIf="!$last"></div>
          </ng-container>
          } 
          <!--to be use @if (assignedInstructor.instruments.length > 1) {
          <div class="remaining-instrument-available-count"  data-toggle="tooltip" [matTooltip]="getInstrumentNames(assignedInstructor.instruments)">
            {{ assignedInstructor.instruments.length - 1 }}+
          </div>
          } -->
         }
        </div>
        <div class="dotted-divider" *ngIf="!$last"></div>

        } }
      </div>
      @if (scheduleDetail?.studentDetails?.length) {
      <div class="dotted-divider"></div>

      <div class="event-student-detail mb-2">
        <div class="d-flex justify-content-between">
          <div class="title">Client Details</div>
          <!-- <div class="age-group">Age Group <span class="primary-color">9-17</span></div> -->
        </div>
        @for (studentDetail of scheduleDetail?.studentDetails; track $index) {
        <div class="schedule-content">
          <div class="placeholder-name">
            <div>
              {{ getInitials(studentDetail.studentName) | uppercase }}
            </div>
          </div>
          <div class="pointer" (click)="openStudentDetails(studentDetail.studentId)">{{ studentDetail.studentName | titlecase }}</div>
        </div>
        <div class="schedule-content ms-30">
          <div class="text-gray me-1">Account Manager:</div>
          <span class="pointer" (click)="openStudentDetails(studentDetail.accountManagerId)">{{ studentDetail.accountManagerName }}</span> 
        </div>
        <div class="d-flex justify-content-between">
          <div class="schedule-content ms-30 text-gray w-170">
            <img [src]="constants.staticImages.icons.email" class="account-manager-detail-icon me-1" alt="" />
            <span class="text-truncate" [matTooltip]="studentDetail.accountManagerEmail">{{ studentDetail.accountManagerEmail }}</span>
          </div>
          <div class="schedule-content text-gray">
            <img [src]="constants.staticImages.icons.phone" class="account-manager-detail-icon me-1" alt="" />
            {{ studentDetail.accountManagerPhoneNo }}
          </div>
        </div>
        @if ($index < scheduleDetail?.studentDetails?.length! - 1) {
        <div class="dotted-divider"></div>
        }
        <!-- <div class="show-all" (click)="showAll = true">Show All (1 More)</div> -->
        }
      </div>
      }
    </div>
    <div class="dotted-divider mt-0"></div>
    <div class="action-btn-wrapper">
      <button mat-raised-button color="accent" class="mat-accent-btn me-2" type="button" (click)="onClosePopup(false)">Close</button>
      @if (!schedulerService.isPastDate(scheduleDetail?.start | localDate)) { @if (scheduleDetail?.classType !== classTypes.MAKE_UP) {
        @if (scheduleDetail?.isCancelSchedule) {
          <button
            *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]"
            mat-raised-button
            color="primary"
            class="mat-primary-btn action-btn me-2"
            [appLoader]="showUndoBtnLoader"
            type="button"
            (click)="scheduleDetail?.classType === classTypes.INTRODUCTORY ? onUndoCancel(scheduleDetail?.id) : isUndoCancelView = true; showCancelLessonView = true"
          >
            Undo Cancel
          </button>
        }
      <button
        *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]"
        mat-raised-button
        color="primary"
        class="mat-red-btn action-btn me-2"
        [appLoader]="showBtnLoader"
        type="button"
        (click)="scheduleDetail?.isCancelSchedule ? onCancelLesson() : showCancelLessonView = true"
      >
        {{ scheduleDetail?.isCancelSchedule ? 'Remove ' : 'Cancel ' }} Lesson
      </button>
      } @if (!scheduleDetail?.isCancelSchedule) {
      <button
        *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]"
        mat-raised-button
        color="primary"
        class="mat-primary-btn action-btn"
        type="button"
        (click)="onEditLesson(scheduleDetail)"
      >
        Edit Lesson
      </button>
      } }
    </div>
  </div>
</ng-template>

<ng-template #cancelLessonBody>
  <div class="schedule-info-wrapper">
    <div class="schedule-header">
      <div class="fw-bold">
        <span *ngIf="scheduleDetail?.isCancelSchedule">Canceled: </span>
        {{ scheduleDetail?.instrumentName }} Lessons ({{ getTimeDiff(scheduleDetail!.start, scheduleDetail!.end) }})
      </div>
      <div class="class-info-wrapper">
        <div>{{ schedulerService.getClassType(scheduleDetail?.classType) }}</div>
      </div>
    </div>
    <div class="schedule-content-wrapper instructor-student-detail">
      <div class="schedule-content">
        <img [src]="constants.staticImages.icons.timeCircleClock" alt="" class="img-icon" />
        <div class="info-content">
          {{ scheduleDetail?.start | localDate | date : 'shortTime' }} - {{ scheduleDetail?.end | localDate | date : 'shortTime' }} -
          {{ scheduleDetail?.start | localDate | date : constants.fullDate }}
        </div>
      </div>
    </div>
    <div class="schedule-content-wrapper w-253 justify-content-between">
      <div class="schedule-content mb-6">
        <img [src]="constants.staticImages.icons.location" alt="" class="img-icon" />
        <div class="info-content">
          {{ scheduleDetail?.locationName }}
        </div>
      </div>
      @if (scheduleDetail?.roomName) {
      <div class="schedule-content">
        <img [src]="constants.staticImages.icons.roomIcon" alt="" class="img-icon" />
        <div class="info-content">{{ scheduleDetail?.roomName }}</div>
      </div>
      }
    </div>
    <div class="schedule-content-wrapper mb-0">
      <div class="schedule-content mb-6">
        <img
          class="instructor-img"
          [src]="
            scheduleDetail?.instructorProfilePhoto
              ? scheduleDetail?.instructorProfilePhoto
              : constants.staticImages.images.profileImgPlaceholder
          "
          alt=""
        />
        <div class="info-content" *ngIf="scheduleDetail?.classType !== classTypes.ENSEMBLE_CLASS">
          <span class="info-label">Instructor </span>
          <span class="primary-text">{{ scheduleDetail?.instructorName }}</span>
        </div>
        <div class="info-content" *ngIf="scheduleDetail?.classType == classTypes.ENSEMBLE_CLASS">
          <span class="info-label">Instructor </span>
          <span class="primary-text">
            @for (assignedInstructors of scheduleDetail?.assignedInstructors; track $index) {
              {{ assignedInstructors?.name }} ,
                  
            } 
          </span>
        </div>
      </div>
      @if (scheduleDetail?.studentDetails?.length) {
      <div class="schedule-content ms-30">
        <img [src]="constants.staticImages.icons.profileCircle" alt="" class="img-icon" />
        <div class="info-content">
          <span class="info-label">Student </span>
          <span class="primary-text"
            >{{ scheduleDetail!.studentDetails[0].studentName | titlecase }} @if (scheduleDetail!.studentDetails.length > 1) { (+
            {{ scheduleDetail!.studentDetails.length - 1 }}) }
          </span>
        </div>
      </div>
      }
    </div>
  </div>
  <div class="dotted-divider"></div>
</ng-template>

<ng-template #cancelLessonPopup>
  @if (isUndoCancelView) {
    <div class="cancel-lesson-wrapper">
      <div class="header-tab-with-btn">
        <div class="back-btn-wrapper" (click)="isUndoCancelView = false">
          <mat-icon>keyboard_arrow_left</mat-icon>
          <span>Undo Cancel</span>
        </div>
      </div>
      <div class="divider"></div>
    <div class="cancel-lesson-body">
      <ng-template [ngTemplateOutlet]="cancelLessonBody"></ng-template>
      <form [formGroup]="cancelScheduleForm">
        @if (scheduleDetail?.classType !== classTypes.INTRODUCTORY) {
        <div class="cancel-lesson-option-wrapper">
          <div class="fw-bold required">Undo Cancel Lesson Option</div>
          <mat-radio-group class="cancel-lesson-option instance" formControlName="isAllInstances">
            <mat-radio-button [value]="false">Only undo this instance</mat-radio-button>
            <mat-radio-button [value]="true">Undo all future instances</mat-radio-button>
          </mat-radio-group>
        </div>
      }
      </form>
    </div>
    <div class="dotted-divider mt-0"></div>
    <div class="action-btn-wrapper">
      <button mat-raised-button color="accent" class="mat-accent-btn me-2" type="button" (click)="showCancelLessonView = false; isUndoCancelView = false">
        Close
      </button>
      <button mat-raised-button color="primary" class="mat-red-btn" type="submit" (click)="onUndoCancel(scheduleDetail?.id)" [appLoader]="showBtnLoader">
        Undo Cancel
      </button>
    </div>
  </div>
  }
  @else {
    <div class="cancel-lesson-wrapper">
      <div class="header-tab-with-btn">
        <div class="back-btn-wrapper" (click)="showCancelLessonView = false">
          <mat-icon>keyboard_arrow_left</mat-icon>
          <span>{{ scheduleDetail?.isCancelSchedule ? 'Remove ' : 'Cancel ' }} Lesson</span>
        </div>
      </div>
      <div class="divider"></div>
      <div class="cancel-lesson-body">
        <ng-template [ngTemplateOutlet]="cancelLessonBody"></ng-template>
        <form [formGroup]="cancelScheduleForm">
          @if (scheduleDetail?.classType !== classTypes.INTRODUCTORY) {
          <div class="cancel-lesson-option-wrapper">
            <div class="fw-bold required">Cancel Lesson Option</div>
            <mat-radio-group class="cancel-lesson-option instance" formControlName="isAllInstances">
              <mat-radio-button [value]="false">Only cancel this instance</mat-radio-button>
              <mat-radio-button [value]="true">Cancel all future instances</mat-radio-button>
            </mat-radio-group>
          </div>
          <div class="dotted-divider"></div>
        }
        @if (isWithinNext24Hours(scheduleDetail?.start | localDate) && scheduleDetail?.classType === classTypes.RECURRING) { 
        <div class="cancel-lesson-option-wrapper">
          <div class="fw-bold required">Would you like to assign make-up pass?</div>
          <mat-radio-group class="cancel-lesson-option appointment" formControlName="isPassGenerated">
            <mat-radio-button [value]="true">Yes</mat-radio-button>
            <mat-radio-button [value]="false">No</mat-radio-button>
          </mat-radio-group>
        </div>
        <div class="dotted-divider"></div>
        }
          <div class="cancel-lesson-option-wrapper">
            <div class="fw-bold required">Remove this Appointment from the calendar?</div>
            <mat-radio-group class="cancel-lesson-option appointment" formControlName="isRemoveAppointment">
              <mat-radio-button [value]="true">Yes</mat-radio-button>
              <mat-radio-button [value]="false">No</mat-radio-button>
            </mat-radio-group>
          </div>
          <div class="dotted-divider"></div>
          <div class="cancel-lesson-option-wrapper">
            <div class="fw-bold mb-1">Notes</div>
            <mat-form-field>
              <textarea
                matInput
                formControlName="notes"
                cdkTextareaAutosize
                cdkAutosizeMinRows="3"
                cdkAutosizeMaxRows="10"
                placeholder="Describe the changes in the schedule to client"
              ></textarea>
            </mat-form-field>
          </div>
        </form>
      </div>
      <div class="dotted-divider mt-0"></div>
      <div class="action-btn-wrapper">
        <button mat-raised-button color="accent" class="mat-accent-btn me-2" type="button" (click)="showCancelLessonView = false">
          Close
        </button>
        <button mat-raised-button color="primary" class="mat-red-btn" type="submit" (click)="onCancelLesson()" [appLoader]="showBtnLoader">
          Yes, {{ scheduleDetail?.isCancelSchedule ? ' Remove' : ' Cancel' }}
        </button>
      </div>
    </div>
  }
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
