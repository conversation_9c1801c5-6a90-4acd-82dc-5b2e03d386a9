<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isContinueToCheckoutSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    class="sidebar-w-750"
    [disableClose]="true">
    @if (isContinueToCheckoutSideNavOpen) {
      <app-continue-to-checkout
        [selectedStudentDetails]="selectedStudentDetails"
        [selectedStudentPlan]="selectedStudentPlan"
        [selectedInstrumentName]="selectedInstrumentName"
        [bookPlanFormValue]="bookPlanFormValue"
        (closeSideNav)="isContinueToCheckoutSideNavOpen = false"
        (closeAllSideNav)="closeAll()"></app-continue-to-checkout>
    }
  </mat-sidenav>
</mat-sidenav-container>

<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="back-btn-wrapper" (click)="closeBookAssignedPlan()">
      <img [src]="constants.staticImages.icons.arrowLeft" class="pointer" alt="" />
      <div class="ps-2">
        <div class="title">Book</div>
        <div class="name">
          <img [src]="constants.staticImages.icons.profileIcon" class="pe-1" alt="" />
          {{ selectedStudentDetails?.firstName | titlecase }}
          {{ selectedStudentDetails?.lastName | titlecase }}
        </div>
      </div>
    </div>
    <div class="action-btn-wrapper">
      <button
        mat-raised-button
        color="accent"
        class="mat-accent-btn back-btn"
        type="button"
        (click)="closeBookAssignedPlan()">
        Close
      </button>
      <button
        mat-raised-button
        color="primary"
        class="mat-primary-btn"
        type="button"
        (click)="onSubmit()"
        [appLoader]="showBtnLoader">
        Continue
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <form [formGroup]="bookPlanForm">
      <div class="field-wrapper">
        <label class="required mb-0">Plan Type</label>
        <div>
          <div class="btn-typed-options-wrapper">
            <div
              [ngClass]="{
                'btn-typed-option': true,
                active: true
              }"
              (click)="setFormControlValue('classType', constants.classTypeOptions[1].value)">
              {{ constants.classTypeOptions[1].label }}
            </div>
          </div>
          <mat-error
            *ngIf="
              !bookPlanForm.controls.classType.value &&
              (bookPlanForm.controls.classType.touched || bookPlanForm.controls.classType.dirty)
            "
            class="mat-error-position">
            <app-error-messages [control]="bookPlanForm.controls.classType"></app-error-messages>
          </mat-error>
        </div>
      </div>

      <div class="field-wrapper">
        <label class="required mb-0">Client Name</label>
        <div class="w-100">
          <div class="plan-info-content">
            {{ selectedStudentDetails?.firstName | titlecase }} {{ selectedStudentDetails?.lastName | titlecase }}
          </div>
        </div>
      </div>
      <div class="field-wrapper">
        <label class="required mb-0">Plan Name</label>
        <div class="w-100">
          <div class="select-plan-info-content plan-info-content">
            <div class="text-truncate pe-4">
              Weekly Music Lessons - {{ selectedInstrumentName }} ({{
                planSummaryService.getPlanType(selectedStudentPlan?.planType!)
              }}
              {{ planSummaryService.getPlanSummary(selectedStudentPlan?.plandetails?.items!) }})
            </div>
            <div>
              (<span class="plan-price">${{ selectedStudentPlan?.planPrice }} /</span> month)
            </div>
          </div>
        </div>
      </div>
      @if (!matchingGrade && selectedInstrumentName !== 'General Music' && selectedInstrumentName !== 'Early Starters') {
        <div class="field-wrapper">
          <label class="required mb-0">Select Skill</label>
          <div>
            <div class="btn-typed-options-wrapper">
              @for (skill of constants.skillOptions | keyvalue: asIsOrder; track $index) {
                <div
                  [ngClass]="{
                    'btn-typed-option': true,
                    active: bookPlanForm.controls.skillType.value === skill.value
                  }"
                  (click)="setFormControlValue('skillType', skill.value); getInstructors()">
                  {{ skill.value }}
                </div>
              }
            </div>
            <mat-error
              *ngIf="
                !bookPlanForm.controls.skillType.value &&
                (bookPlanForm.controls.skillType.touched || bookPlanForm.controls.skillType.dirty)
              "
              class="mat-error-position">
              <app-error-messages [control]="bookPlanForm.controls.skillType"></app-error-messages>
            </mat-error>
          </div>
        </div>
      }
      <div class="field-wrapper">
        <label class="required mb-0">Select Lesson Type</label>
        <div>
          <div class="single-btn-select-wrapper">
            @for (lessonType of constants.lessonTypeValueOptions; track $index) {
              <div
                [ngClass]="{ active: bookPlanForm.controls.lessonType.value === lessonType.value }"
                class="select-btn"
                (click)="setFormControlValue('lessonType', lessonType.value)">
                {{ lessonType.label }}
              </div>
            }
          </div>
          <mat-error
            *ngIf="
              !bookPlanForm.controls.lessonType.value &&
              (bookPlanForm.controls.lessonType.touched || bookPlanForm.controls.lessonType.dirty)
            "
            class="mat-error-position">
            <app-error-messages [control]="bookPlanForm.controls.lessonType"></app-error-messages>
          </mat-error>
        </div>
      </div>
      <div class="field-wrapper field-with-mat-inputs">
        <label class="required">Select Location</label>
        <div class="w-100">
          <mat-form-field class="w-100 mat-select-custom">
            <mat-select formControlName="locationId" placeholder="Select Location" (ngModelChange)="getInstructors()">
              <mat-option *ngFor="let location of locations" [value]="location.schoolLocations.id">
                {{ location.schoolLocations.locationName }}
              </mat-option>
            </mat-select>
            <mat-error>
              <app-error-messages [control]="bookPlanForm.controls.locationId"></app-error-messages>
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="field-wrapper field-with-mat-inputs">
        <label class="required">Start Date</label>
        <div class="w-100">
          <mat-form-field class="mat-start-date">
            <input
              matInput
              (dateChange)="getSuggestedTimeAndInstructors(); getDayOfWeek()"
              [matDatepicker]="picker"
              (click)="picker.open()"
              formControlName="scheduleDate"
              placeholder="Select Start Date"
              [min]="maxDate" />
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <mat-error>
              <app-error-messages [control]="bookPlanForm.controls.scheduleDate"></app-error-messages>
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="field-wrapper">
        <label class="required mb-0">Select days of the week</label>
        <div>
          <div class="single-btn-select-wrapper">
            @for (day of constants.daysOfTheWeek; track $index) {
              <div
                [ngClass]="{ active: isDaySelected(day.value) }"
                class="select-btn"
                (click)="setDaysOfWeek(day.value)">
                {{ day.label }}
              </div>
            }
          </div>
          <mat-error
            *ngIf="
              !bookPlanForm.controls.daysOfSchedule.value.length &&
              bookPlanForm.controls.daysOfSchedule.errors?.['required']
            "
            class="mat-error-position">
            <app-error-messages [control]="bookPlanForm.controls.daysOfSchedule"></app-error-messages>
          </mat-error>
        </div>
      </div>
      @if (
        getInstructorAvailability.scheduleStartDate &&
        getInstructorAvailability.daysOfSchedule?.length &&
        getInstructorAvailability.locationId &&
        getInstructorAvailability.planId
      ) {
        <div class="field-wrapper field-with-mat-inputs">
          <label class="required">Select Instructor</label>
          <div class="w-100">
            <mat-form-field class="w-100 mat-select-custom">
              <mat-select
                formControlName="instructorId"
                placeholder="Select Instructor"
                (ngModelChange)="getSuggestedTime()">
                @if (instructors?.length) {
                  <mat-option
                    *ngFor="let instructor of instructors"
                    [value]="instructor?.id"
                    [disabled]="!instructor.isAvailable">
                    <div class="instructor-list">
                      <div class="instructor-name">{{ instructor?.name }}</div>
                      <div *ngIf="!instructor.isAvailable" class="text-red">Busy</div>
                    </div>
                  </mat-option>
                } @else {
                  <mat-option>No Instructor Available</mat-option>
                }
              </mat-select>
              <mat-error>
                <app-error-messages [control]="bookPlanForm.controls.instructorId"></app-error-messages>
              </mat-error>
            </mat-form-field>
          </div>
        </div>
      }
      @if (getInstructorAvailability.instructorId) {
        <div class="field-wrapper">
          <label class="required mb-0">Select Time Slot</label>
          <div class="w-100">
            <mat-form-field class="w-100 mat-select-custom time">
              <mat-select placeholder="Select Time" [(value)]="selectedTimeSlot">
                @if (getInstructorAvailability.instructorId && suggestedTimeSlots?.length) {
                  <mat-option
                    *ngFor="let suggestedTimeSlot of suggestedTimeSlots"
                    (click)="setStartAndEndTime(suggestedTimeSlot)"
                    [value]="suggestedTimeSlot.startTime + ' - ' + suggestedTimeSlot.endTime">
                    {{ suggestedTimeSlot.startTime | date: "shortTime" }} -
                    {{ suggestedTimeSlot.endTime | date: "shortTime" }}
                  </mat-option>
                } @else if (!suggestedTimeSlots?.length && !bookPlanForm.controls.instructorId.value) {
                  <mat-option>Please select an Instructor to see time slots</mat-option>
                }
                @else {
                  <mat-option>No Time Slot Available</mat-option>
                }
              </mat-select>
            </mat-form-field>
            <mat-error
              *ngIf="
                !bookPlanForm.controls.scheduleStartTime.value &&
                (bookPlanForm.controls.scheduleStartTime.touched || bookPlanForm.controls.scheduleStartTime.dirty)
              "
              class="mat-error-position">
              <app-error-messages [control]="bookPlanForm.controls.scheduleStartTime"></app-error-messages>
            </mat-error>
          </div>
        </div>
      }
      <div class="field-wrapper mt-2">
        <label class="form-label m-0">Would you like to:</label>
        <mat-radio-group formControlName="isClientRequest"
          class="d-flex gap-4">
          <mat-radio-button [value]="false">
            Continue booking from here
          </mat-radio-button>
          <mat-radio-button [value]="true">
            Send documents to client for review
          </mat-radio-button>
        </mat-radio-group>
      </div>
    </form>
  </div>
</div>
