<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav [opened]="isPlanRenewSideNavOpen" mode="over" position="end" fixedInViewport="true"
    class="sidebar-w-750" [disableClose]="true">
    @if(isPlanRenewSideNavOpen) {
      <app-continue-to-checkout
        [selectedStudentDetails]="selectedStudentDetails"
        [selectedStudentPlan]="selectedStudentPlan"
        [selectedInstrumentName]="selectedInstrumentName"
        [bookPlanFormValue]="bookPlanFormValue"
        [isPlanRenewal]="true"
        (closeSideNav)="isPlanRenewSideNavOpen = false"
        (closeAllSideNav)="isPlanRenewSideNavOpen = false; getStudentPlansExpiringSoon()"
      ></app-continue-to-checkout>
    }
  </mat-sidenav>
</mat-sidenav-container>

<div class="auth-page-with-header">
  <div class="search-and-count-wrapper-auth">
    <div class="search-and-count-wrapper">
      <div class="total-users">
        Total: <span class="border-0">{{ totalCount }}</span>
      </div>
    </div>
  </div>
  <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : studentPlanLists"></ng-container>
</div>

<ng-template #studentPlanLists>
  <ng-container [ngTemplateOutlet]="studentPlans && studentPlans.length ? studentPlanList : noDataFound"></ng-container>
</ng-template>

<ng-template #studentPlanList>
  <div class="visits-list">
    @for (studentPlan of studentPlans; track $index) {
    <div class="o-card mb-2">
      <div class="o-card-body">
        <div class="pointer" (click)="openStudentDetailPage(studentPlan.studentplan.dependentInformationId)">
          <div class="title">
            @if(studentPlan.isEnsembleAvailable) {
              Weekly Ensemble Lessons ({{ planSummaryService.getPlanType(studentPlan.planType) }}-{{
                planSummaryService.getPlanSummary(studentPlan.planDetails)
              }})
            }
            @else {
              Weekly {{ studentPlan.instrumentName }} Lessons ({{ planSummaryService.getPlanType(studentPlan.planType) }}-{{
                planSummaryService.getPlanSummary(studentPlan.planDetails)
              }})
            }
          </div>

          <div class="visit-content">
            <div class="visit-info">
              <div class="me-1 text-black">Dependent</div>
              <div class="primary-color">
                {{ studentPlan.dependentName | titlecase }}
              </div>
            </div>
            <div class="dot"></div>
            <div class="visit-info">
              <div class="text-gray">
                {{ studentPlan.startDate | date: 'mediumDate' }} - {{ studentPlan.endDate | date: 'mediumDate' }}
              </div>
            </div>
            @if(currentDate < studentPlan.endDate) {
              <div class="dot"></div>
              <div class="visit-info">
                  Expires in 
                <div class="primary-color ms-1">
                  {{ getRemainingDays(studentPlan.endDate) }} Days
                </div>
              </div>
            }
          </div>
        </div>

        <div class="visit-cancel-info">
          @if (!studentPlan.updatedAssignedPlanStatus) {
            <button
              [appLoader]="showBtnLoaderId === studentPlan.studentplan.id"
              mat-raised-button
              color="primary"
              class="mat-primary-btn"
              type="button"
              (click)="togglePlanRenewalSideNav(true, studentPlan)"
            >
              Renew Plan
            </button>
          }
          @else if (studentPlan.updatedAssignedPlanStatus === assignedPlanStatuses.SCHEDULE_CREATED && !studentPlan.isAgreementDone) {
            <button
              [appLoader]="showBtnLoaderId === studentPlan.studentplan.id"
              mat-raised-button
              color="primary"
              class="mat-primary-btn"
              type="button"
              (click)="togglePlanRenewalSideNav(true, studentPlan)"
            >
              Accept T&C
            </button>
          }
          @else if (studentPlan.isAgreementDone && !studentPlan.isPaid) {
            <button
              [appLoader]="showBtnLoaderId === studentPlan.studentplan.id"
              mat-raised-button
              color="primary"
              class="mat-primary-btn"
              type="button"
              (click)="togglePlanRenewalSideNav(true, studentPlan)"
            >
              Payment
            </button>
          }
          @else if (studentPlan.updatedAssignedPlanStatus === assignedPlanStatuses.PAYMENT_DONE) {
            <div class="primary-color fs-6">Renewed</div>
          }
        </div>
      </div>
    </div>
    }
  </div>
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-card">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>